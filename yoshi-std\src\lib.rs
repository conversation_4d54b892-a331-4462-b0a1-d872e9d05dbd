/* yoshi-std/src/lib.rs */

#![warn(clippy::all)]
#![warn(missing_docs)]
#![warn(clippy::cargo)]
#![warn(clippy::pedantic)]
#![allow(unexpected_cfgs)] // Allow experimental feature flags
#![allow(clippy::too_many_lines)] // Comprehensive error handling requires extensive implementation
#![allow(clippy::result_large_err)] // Error context richness justifies larger error types
#![allow(clippy::enum_variant_names)] // Consistent naming like YoshiKind variants
#![allow(clippy::items_after_statements)] // Better code organization in some contexts
#![allow(clippy::module_name_repetitions)] // Allow YoshiKind, YoContext naming
#![cfg_attr(not(feature = "std"), no_std)] // No-std compatibility

//! # Yoshi Std - Standard Library Extensions for Yoshi Error Framework
//!
//! This crate provides standard library-specific enhancements and utilities for the Yoshi
//! error handling framework. It re-exports all core functionality from `yoshi-core` and
//! adds std-specific features like enhanced backtrace support, async utilities, and
//! integration with standard library error types.
//!
//! ## Module Classification
//! - **Performance-Critical**: Sub-microsecond error creation with O(1) context attachment
//! - **Complexity Level**: Expert-level error handling with beginner-friendly APIs
//! - **API Stability**: Stable with semantic versioning guarantees
//!
//! ## Architecture
//!
//! This crate serves as the standard library layer of the Yoshi ecosystem:
//!
//! - **Core Re-exports**: All fundamental types from `yoshi-core`
//! - **Std Enhancements**: Additional functionality requiring standard library
//! - **Integration Layer**: Seamless integration with std error types
//! - **Async Support**: Tokio and async-std compatibility (feature-gated)
//!
//! ## Core Types (Re-exported from yoshi-core)
//!
//! - [`Yoshi`]: The main error type providing structured error handling
//! - [`YoshiKind`]: Error categories with type-specific fields
//! - [`YoContext`]: Contextual information and metadata
//! - [`HatchExt`]: Extension trait for `Result` types
//! - [`YoshiLocation`]: Source code location capture
//! - [`YoshiBacktrace`]: Performance-monitored backtrace wrapper
//! - [`Result`]: Type alias for `Result` with `Yoshi` as default error
//! - [`error_instance_count()`]: Global counter for Yoshi error instances
//!
//! ## Feature Flags
//!
//! ```toml
//! [dependencies]
//! yoshi-std = { version = "0.1", features = ["std", "serde", "async"] }
//! ```
//!
//! - **`std`** (default): Standard library integration with backtrace support
//! - **`serde`**: Serialization support for error persistence and transmission
//! - **`async`**: Tokio integration and async utilities
//! - **`tracing`**: Integration with the tracing ecosystem
//!
//! # Examples
//!
//! Basic error creation and context addition:
//!
//! ```rust
//! use yoshi_std::{Yoshi, YoshiKind, HatchExt};
//! # use std::io;
//! # use std::io::ErrorKind;
//! #
//! # fn simulate_io_error() -> Result<(), io::Error> {
//! #    Err(io::Error::new(ErrorKind::PermissionDenied, "cannot access file"))
//! # }
//!
//! fn load_config(path: &str) -> Result<String, Yoshi> {
//!     // Convert I/O errors to Yoshi errors with additional context
//!     simulate_io_error()
//!         .map_err(Yoshi::from)?;
//!
//!     // Errors can be built up with context as they propagate
//!     Err(Yoshi::new(YoshiKind::NotFound {
//!         resource_type: "config file".into(),
//!         identifier: path.into(),
//!         search_locations: None,
//!     })
//!     .with_metadata("config_path", path)
//!     .with_suggestion("Ensure the configuration file exists and is readable")
//!     .context(format!("Failed to load configuration from {}", path)))
//! }
//!
//! # fn main() {
//! match load_config("/etc/app/config.json") {
//!     Ok(config) => println!("Loaded: {}", config),
//!     Err(error) => {
//!         eprintln!("Configuration error: {}", error);
//!         // Rich error output includes context, metadata, and suggestions
//!     }
//! }
//! # }
//! ```

//============================================================================
// CORE RE-EXPORTS FROM YOSHI-CORE
//============================================================================

// Re-export all core functionality from yoshi-core
pub use yoshi_core::*;

//============================================================================
// STD-SPECIFIC ENHANCEMENTS AND UTILITIES
//============================================================================

/// Standard library specific error conversion utilities
#[cfg(feature = "std")]
pub mod std_integration {

    use super::{Yoshi, YoshiKind};
    use std::error::Error as StdError;

    /// Convert any standard library error into a Yoshi error with context preservation.
    ///
    /// This function provides a convenient way to convert standard library errors
    /// into the Yoshi ecosystem while preserving the original error information
    /// and adding structured context.
    ///
    /// # Examples
    ///
    /// ```rust
    /// use yoshi_std::std_integration::from_std_error;
    /// use std::fs;
    ///
    /// let result = fs::read_to_string("nonexistent.txt")
    ///     .map_err(|e| from_std_error(e, "file_operation"));
    /// ```
    pub fn from_std_error<E: StdError + Send + Sync + 'static>(error: E, operation: &str) -> Yoshi {
        Yoshi::new(YoshiKind::Internal {
            message: format!("{operation}: {error}").into(),
            source: None,
            component: Some(operation.into()),
        })
        .with_metadata("operation", operation)
        .with_metadata("error_type", std::any::type_name::<E>())
    }
}

/// Async utilities for error handling (requires 'async' feature)
#[cfg(feature = "async")]
pub mod async_utils {

    use super::{Result, Yoshi, YoshiKind};
    use tokio::time::{timeout, Duration};

    /// Timeout wrapper that converts timeout errors to Yoshi errors
    ///
    /// # Examples
    ///
    /// ```rust,no_run
    /// use yoshi_std::async_utils::with_timeout;
    /// use tokio::time::Duration;
    ///
    /// # async fn example() -> Result<(), yoshi_std::Yoshi> {
    /// let result = with_timeout(
    ///     Duration::from_secs(5),
    ///     async { tokio::time::sleep(Duration::from_secs(10)).await },
    ///     "long_operation"
    /// ).await?;
    /// # Ok(())
    /// # }
    /// ```
    ///
    /// # Errors
    ///
    /// Returns a `Yoshi` error if the operation times out or if the underlying future fails.
    pub async fn with_timeout<F, T>(
        duration: Duration,
        future: F,
        operation_name: &str,
    ) -> Result<T, Yoshi>
    where
        F: std::future::Future<Output = T>,
    {
        timeout(duration, future).await.map_err(|_| {
            Yoshi::new(YoshiKind::Timeout {
                operation: operation_name.into(),
                duration,
                expected_max: Some(duration),
            })
            .with_metadata("timeout_type", "tokio_timeout")
            .context(format!(
                "Operation '{operation_name}' timed out after {duration:?}"
            ))
        })
    }
}

/// Tracing integration utilities (requires 'tracing' feature)
#[cfg(feature = "tracing")]
pub mod tracing_integration {

    use super::Yoshi;

    /// Log a Yoshi error using tracing with appropriate level based on severity
    ///
    /// # Examples
    ///
    /// ```rust
    /// use yoshi_std::{Yoshi, YoshiKind};
    /// use yoshi_std::tracing_integration::trace_error;
    ///
    /// let error = Yoshi::new(YoshiKind::Internal {
    ///     message: "Database connection failed".into(),
    ///     source: None,
    ///     component: Some("user_service".into()),
    /// });
    ///
    /// trace_error(&error);
    /// ```
    pub fn trace_error(error: &Yoshi) {
        let severity = error.kind().severity();

        match severity {
            0..=20 => tracing::debug!("Low severity error: {}", error),
            21..=40 => tracing::info!("Medium severity error: {}", error),
            41..=60 => tracing::warn!("High severity error: {}", error),
            61..=80 => tracing::error!("Critical error: {}", error),
            _ => tracing::error!("Fatal error: {}", error),
        }
    }
}
