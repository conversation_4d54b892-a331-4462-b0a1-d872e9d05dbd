# Generated by Cargo
# This file is automatically @generated by Cargo, and may be overwritten
# At any time without warning.
# If you want to customize this file, do not use `cargo add` or `cargo remove`.
# See https://doc.rust-lang.org/cargo/reference/manifest.html#autobins
# for more info.

# Added by an IDE.
.idea/

# Dotenv file
.env

# MacOs finder files
.DS_Store

# Log files
*.log
log/
logs/

# Compiled output
/target/

# Tool-specific backup files
*.yoshibackup
*.rs.yoshibackup

# Remove Cargo.lock from gitignore if creating an application, leave it for libraries
# to allow testing against a wide range of dependency versions.
Cargo.lock

# Secret files
secrets.toml

# Environment-specific files
debug/
release/

# Rust-analyzer cache
/.rust-analyzer
/rust-analyzer

# files
lib copy.rs
deluxe.lib.rs.txt
