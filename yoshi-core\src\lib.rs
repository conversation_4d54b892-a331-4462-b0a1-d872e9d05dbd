//============================================================================
// SECTION 1: CORE FOUNDATIONS & COMPILATION DIRECTIVES
//============================================================================

#![no_std]
#![warn(clippy::all)]
#![warn(missing_docs)]
#![warn(clippy::cargo)]
#![warn(clippy::pedantic)]
#![allow(unexpected_cfgs)] // Allow experimental feature flags
#![allow(clippy::too_many_lines)] // Comprehensive error handling requires extensive implementation
#![allow(clippy::result_large_err)] // Error context richness justifies larger error types
#![allow(clippy::enum_variant_names)] // Consistent naming like YoshiKind variants
#![allow(clippy::items_after_statements)] // Better code organization in some contexts
#![allow(clippy::module_name_repetitions)] // Allow YoshiKind, YoContext naming

//! # Yoshi Core - The `no_std` Error Handling Foundation
//!
//! This crate provides the foundational, `no_std`-compatible error types, traits,
//! and data structures for the entire Yoshi error handling ecosystem. It serves as the
//! stable API contract that other `yoshi-*` crates depend on.
//!
//! ## Design Philosophy
//!
//! This crate contains **definitions only** and is `alloc`-dependent. The primary
//! implementation logic lives in the `yoshi-std` crate. This separation ensures:
//!
//! - **API Stability**: The API contract remains stable for long periods
//! - **Modularity**: Other crates can depend on lightweight API without implementations
//! - **Clarity**: Crystal-clear distinction between "what it is" vs "how it works"
//!
//! ## Core Architecture
//!
//! ```rust
//! use yoshi_core::{Yoshi, YoshiKind, Hatch, yoshi_location};
//!
//! // Basic error creation (minimal constructor)
//! let error = Yoshi::new(YoshiKind::Internal {
//!     message: "operation failed".into(),
//!     source: None,
//!     component: None,
//! });
//!
//! // Location capture at compile time
//! let location = yoshi_location!();
//! println!("Error at: {}", location);
//! ```
//!
//! ## Core Definitions
//!
//! - **Core Types**: [`Yoshi`], [`YoshiKind`], [`YoContext`], [`YoshiLocation`]
//! - **Traits**: [`HatchExt`], [`LayText`], [`Hatchable`]
//! - **Thematic Aliases**: [`Hatch<T>`] for `Result<T, Yoshi>`
//! - **Macros**: [`yoshi_location!`] for compile-time source location capture
//! - **No_std Support**: [`SystemTime`], [`ThreadId`], [`NoStdIo`], [`YoshiBacktrace`]
//!
//! ## Feature Flags
//!
//! - **`alloc`** (default): Required for core functionality
//! - **`serde`**: Serialization support for core types

// Unified imports with feature detection for maximum compatibility
#[cfg(feature = "alloc")]
extern crate alloc;

#[cfg(feature = "alloc")]
pub use alloc::{
    boxed::Box,
    format,
    string::{String, ToString},
    sync::Arc,
    vec,
    vec::Vec,
};

#[cfg(not(feature = "alloc"))]
compile_error!("yoshi-core requires the 'alloc' feature for core functionality");

use core::any::Any;
use core::error::Error;
use core::fmt::{self, Display, Formatter};
use core::sync::atomic::{AtomicU32, AtomicU64, Ordering};
use core::time::Duration;

// Conditional imports for enhanced features
#[cfg(feature = "alloc")]
use alloc::collections::BTreeMap as HashMap;

// Add serde helper functions for Arc<str> serialization
#[cfg(feature = "serde")]
mod serde_helpers {
    use super::String;
    use serde::{Deserialize, Deserializer, Serialize, Serializer};
    use alloc::collections::BTreeMap as HashMap;
    use alloc::sync::Arc;

    /// Serialize `Option<Arc<str>>` as `Option<String>`
    #[allow(clippy::ref_option)]
    pub fn serialize_arc_str<S>(value: &Option<Arc<str>>, serializer: S) -> Result<S::Ok, S::Error>
    where
        S: Serializer,
    {
        value
            .as_ref()
            .map(core::convert::AsRef::as_ref)
            .serialize(serializer)
    }

    /// Deserialize `Option<String>` as `Option<Arc<str>>`
    pub fn deserialize_arc_str<'de, D>(deserializer: D) -> Result<Option<Arc<str>>, D::Error>
    where
        D: Deserializer<'de>,
    {
        let opt_string: Option<String> = Option::deserialize(deserializer)?;
        Ok(opt_string.map(|s| Arc::from(s.as_str())))
    }

    /// Serialize `HashMap<Arc<str>, Arc<str>>` as `HashMap<String, String>`
    pub fn serialize_arc_str_map<S>(
        value: &HashMap<Arc<str>, Arc<str>>,
        serializer: S,
    ) -> Result<S::Ok, S::Error>
    where
        S: Serializer,
    {
        let string_map: HashMap<&str, &str> = value
            .iter()
            .map(|(k, v)| (k.as_ref(), v.as_ref()))
            .collect();
        string_map.serialize(serializer)
    }

    /// Deserialize `HashMap<String, String>` as `HashMap<Arc<str>, Arc<str>>`
    pub fn deserialize_arc_str_map<'de, D>(
        deserializer: D,
    ) -> Result<HashMap<Arc<str>, Arc<str>>, D::Error>
    where
        D: Deserializer<'de>,
    {
        let string_map: HashMap<String, String> = HashMap::deserialize(deserializer)?;
        Ok(string_map
            .into_iter()
            .map(|(k, v)| (Arc::from(k.as_str()), Arc::from(v.as_str())))
            .collect())
    }
}

#[cfg(feature = "serde")]
use serde_helpers::{
    deserialize_arc_str, deserialize_arc_str_map, serialize_arc_str, serialize_arc_str_map,
};

//============================================================================
// SECTION 2: GLOBAL STATE AND UTILITIES
//============================================================================

/// Global error instance counter for debugging and performance monitoring.
///
/// This atomic counter tracks the total number of [`Yoshi`] error instances
/// that have been created since application start. Used for:
/// - Performance monitoring and bottleneck detection
/// - Error correlation in distributed systems
/// - Memory usage analysis and optimization
pub static ERROR_INSTANCE_COUNTER: AtomicU32 = AtomicU32::new(0);

/// Gets the current number of Yoshi error instances created.
///
/// This function provides insight into error creation patterns and can be useful
/// for performance monitoring, memory usage analysis, and detecting error-heavy
/// code paths that might benefit from optimization.
///
/// # Returns
///
/// The total number of [`Yoshi`] error instances created since application start.
///
/// # Examples
///
/// ```rust
/// use yoshi_core::{Yoshi, YoshiKind, error_instance_count};
///
/// let initial_count = error_instance_count();
/// let _err = Yoshi::new(YoshiKind::Internal {
///     message: "test error".into(),
///     source: None,
///     component: None,
/// });
/// assert_eq!(error_instance_count(), initial_count + 1);
/// ```
pub fn error_instance_count() -> u32 {
    ERROR_INSTANCE_COUNTER.load(Ordering::Relaxed)
}

/// Resets the global error instance counter (testing only).
///
/// This function is only available in test builds and should never be used
/// in production code. It exists to ensure test isolation and predictable
/// counter values in test suites.
#[cfg(test)]
#[inline]
pub fn reset_error_instance_counter() {
    ERROR_INSTANCE_COUNTER.store(0, Ordering::Relaxed);
}

//============================================================================
// SECTION 3: NO_STD COMPATIBILITY LAYER
//============================================================================

/// Enhanced SystemTime for `no_std` environments with monotonic counter.
///
/// In `no_std` environments where `std::time::SystemTime` is not available,
/// this provides a monotonic timestamp suitable for ordering events and
/// measuring relative time differences.
///
/// # Limitations
///
/// - Not wall-clock time - only useful for ordering and relative measurements
/// - Timestamp counter may wrap after extremely long periods
/// - No timezone or calendar functionality
///
/// # Examples
///
/// ```rust
/// use yoshi_core::SystemTime;
///
/// let t1 = SystemTime::now();
/// // ... some operation ...
/// let t2 = SystemTime::now();
///
/// assert!(t2.timestamp() > t1.timestamp());
/// ```
#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord)]
#[cfg_attr(feature = "serde", derive(serde::Serialize, serde::Deserialize))]
pub struct SystemTime {
    /// Monotonic timestamp counter for ordering events
    timestamp: u64,
}

impl SystemTime {
    /// Returns a SystemTime with monotonic ordering guarantees.
    ///
    /// While not wall-clock time, this provides ordering semantics
    /// useful for debugging and event correlation in no_std environments.
    ///
    /// # Examples
    ///
    /// ```rust
    /// use yoshi_core::SystemTime;
    ///
    /// let now = SystemTime::now();
    /// assert!(now.timestamp() > 0);
    /// ```
    pub fn now() -> Self {
        static COUNTER: AtomicU64 = AtomicU64::new(0);
        Self {
            timestamp: COUNTER.fetch_add(1, Ordering::Relaxed),
        }
    }

    /// Returns the internal timestamp for debugging purposes.
    ///
    /// This value is only meaningful relative to other SystemTime instances
    /// created in the same application run. It should not be persisted or
    /// compared across application restarts.
    pub const fn timestamp(&self) -> u64 {
        self.timestamp
    }

    /// Calculates duration since another SystemTime (in timestamp units).
    ///
    /// # Arguments
    ///
    /// * `earlier` - The earlier SystemTime to calculate duration from
    ///
    /// # Returns
    ///
    /// `Some(duration)` if this SystemTime is later than `earlier`,
    /// `None` if this SystemTime is earlier (negative duration not supported)
    pub const fn duration_since(&self, earlier: SystemTime) -> Option<u64> {
        if self.timestamp >= earlier.timestamp {
            Some(self.timestamp - earlier.timestamp)
        } else {
            None
        }
    }

    /// Returns elapsed timestamp units since this SystemTime.
    ///
    /// This is equivalent to `SystemTime::now().duration_since(*self).unwrap_or(0)`.
    pub fn elapsed(&self) -> u64 {
        Self::now().timestamp.saturating_sub(self.timestamp)
    }
}

/// Enhanced ThreadId for `no_std` environments with unique identification.
///
/// Provides unique thread identification in environments where
/// `std::thread::ThreadId` is not available. Useful for correlating
/// errors across different execution contexts.
///
/// # Examples
///
/// ```rust
/// use yoshi_core::ThreadId;
///
/// let thread_id = ThreadId::current();
/// println!("Current thread: {}", thread_id);
/// ```
#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord, Hash)]
pub struct ThreadId {
    /// Unique identifier for tracking execution contexts
    id: u32,
}

impl ThreadId {
    /// Returns a ThreadId with unique identification.
    ///
    /// In no_std environments, this provides unique identifiers
    /// useful for correlating errors across different execution contexts.
    /// Each call returns a unique ThreadId, even from the same thread.
    ///
    /// # Examples
    ///
    /// ```rust
    /// use yoshi_core::ThreadId;
    ///
    /// let id1 = ThreadId::current();
    /// let id2 = ThreadId::current();
    /// assert_ne!(id1, id2); // Each call gets unique ID
    /// ```
    pub fn current() -> Self {
        static THREAD_COUNTER: AtomicU32 = AtomicU32::new(1);
        Self {
            id: THREAD_COUNTER.fetch_add(1, Ordering::Relaxed),
        }
    }

    /// Returns the raw thread ID for debugging.
    #[inline]
    pub const fn as_u32(&self) -> u32 {
        self.id
    }

    /// Creates a ThreadId from a raw ID (for testing/debugging).
    ///
    /// # Arguments
    ///
    /// * `id` - The raw thread identifier
    ///
    /// # Examples
    ///
    /// ```rust
    /// use yoshi_core::ThreadId;
    ///
    /// let thread_id = ThreadId::from_u32(42);
    /// assert_eq!(thread_id.as_u32(), 42);
    /// ```
    pub const fn from_u32(id: u32) -> Self {
        Self { id }
    }
}

impl Display for ThreadId {
    fn fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result {
        write!(f, "ThreadId({})", self.id)
    }
}

//============================================================================
// SECTION 4: STRUCTURED ERROR CLASSIFICATION SYSTEM
//============================================================================

/// High‑level categories for recoverable failures with performance optimizations.
///
/// This enum represents the fundamental classification of an error within the
/// Yoshi framework. Each variant provides specific fields relevant to its
/// error category, enabling rich, structured error reporting and programmatic
/// error handling.
///
/// # Design Principles
///
/// - **Type Safety**: Each error category has relevant, typed fields
/// - **Performance**: Uses `Arc<str>` for efficient string sharing
/// - **Extensibility**: `#[non_exhaustive]` allows adding variants without breaking changes
/// - **Clarity**: Self-documenting variant names and field names
///
/// # Examples
///
/// ```rust
/// use yoshi_core::{Yoshi, YoshiKind};
/// use std::time::Duration;
///
/// // Network error with structured information
/// let network_error = Yoshi::new(YoshiKind::Network {
///     message: "Connection refused".into(),
///     source: None,
///     error_code: Some(111), // ECONNREFUSED
/// });
///
/// // Validation error with expected/actual context
/// let validation_error = Yoshi::new(YoshiKind::Validation {
///     field: "email".into(),
///     message: "Invalid email format".into(),
///     expected: Some("<EMAIL>".into()),
///     actual: Some("invalid-email".into()),
/// });
/// ```
#[derive(Debug, Clone)]
#[non_exhaustive]
pub enum YoshiKind {
    /// I/O failure in `no_std` with enhanced error categorization.
    ///
    /// This variant wraps [`NoStdIo`] when the `std` feature is not enabled,
    /// providing structured I/O error handling in embedded and no_std environments.
    Io(NoStdIo),

    /// Standard I/O failure with std::io::Error support (std only).
    ///
    /// This variant is only available when the `std` feature is enabled,
    /// providing direct integration with the standard library's I/O error type.
    #[cfg(feature = "std")]
    StdIo(std::io::Error),

    /// Network-related error with connection and protocol context.
    ///
    /// This variant represents errors that occur during network operations,
    /// including connectivity issues, protocol errors, and communication failures.
    /// Includes optional error codes for protocol-specific diagnostics.
    ///
    /// # Fields
    ///
    /// * `message` - Human-readable description of the network error
    /// * `source` - Optional nested [`Yoshi`] error that caused this network issue
    /// * `error_code` - Optional numeric error code (e.g., HTTP status, errno)
    Network {
        /// A human-readable description of the network error.
        message: Arc<str>,
        /// An optional nested [`Yoshi`] error that caused this network issue.
        source: Option<Box<Yoshi>>,
        /// An optional network-specific error code (e.g., HTTP status code).
        error_code: Option<u32>,
    },

    /// Configuration error with enhanced diagnostics.
    ///
    /// Represents errors in application configuration, including missing values,
    /// invalid formats, and configuration file access issues.
    ///
    /// # Fields
    ///
    /// * `message` - Description of the configuration error
    /// * `source` - Optional nested error that caused this configuration issue
    /// * `config_path` - Optional path to the configuration file or source
    Config {
        /// A human-readable description of the configuration error.
        message: Arc<str>,
        /// An optional nested [`Yoshi`] error that caused this configuration issue.
        source: Option<Box<Yoshi>>,
        /// An optional path to the configuration file or source.
        config_path: Option<Arc<str>>,
    },

    /// Data validation failure with field-level precision.
    ///
    /// Represents validation errors with detailed context about what was expected
    /// versus what was actually provided. Ideal for form validation, API input
    /// validation, and data integrity checking.
    ///
    /// # Fields
    ///
    /// * `field` - The name of the field that failed validation
    /// * `message` - Description of why validation failed
    /// * `expected` - Optional description of expected value/format
    /// * `actual` - Optional string representation of actual value received
    Validation {
        /// The name of the field that failed validation.
        field: Arc<str>,
        /// A description of why the validation failed.
        message: Arc<str>,
        /// An optional description of the expected value or format.
        expected: Option<Arc<str>>,
        /// An optional string representation of the actual value received.
        actual: Option<Arc<str>>,
    },

    /// Internal invariant breakage with debugging context.
    ///
    /// This typically indicates a bug within the application's own logic
    /// or an unexpected state. Should be used sparingly and usually indicates
    /// a programming error rather than user error.
    ///
    /// # Fields
    ///
    /// * `message` - Description of the internal error
    /// * `source` - Optional nested error that caused this internal issue
    /// * `component` - Optional name of the component where the error occurred
    Internal {
        /// A description of the internal error.
        message: Arc<str>,
        /// An optional nested [`Yoshi`] error that caused this internal issue.
        source: Option<Box<Yoshi>>,
        /// An optional name of the component where the error occurred.
        component: Option<Arc<str>>,
    },

    /// Resource not found with typed identification.
    ///
    /// Represents missing resources with structured information about what
    /// was being searched for and where. Useful for file systems, databases,
    /// API endpoints, and other resource-based operations.
    ///
    /// # Fields
    ///
    /// * `resource_type` - Type of resource (e.g., "User", "File", "Endpoint")
    /// * `identifier` - Specific identifier that was not found
    /// * `search_locations` - Optional list of locations where resource was searched
    NotFound {
        /// The type of resource (e.g., "User", "Product", "File").
        resource_type: Arc<str>,
        /// The specific identifier of the resource that was not found.
        identifier: Arc<str>,
        /// Optional list of locations where the resource was searched.
        search_locations: Option<Vec<Arc<str>>>,
    },

    /// Operation timeout with detailed timing information.
    ///
    /// Represents operations that exceeded their allocated time budget.
    /// Includes actual duration and expected maximum for debugging and
    /// configuration adjustment.
    ///
    /// # Fields
    ///
    /// * `operation` - Description of the operation that timed out
    /// * `duration` - How long the operation ran before timing out
    /// * `expected_max` - Optional maximum expected duration for comparison
    Timeout {
        /// A description of the operation that timed out.
        operation: Arc<str>,
        /// The duration for which the operation ran before timing out.
        duration: Duration,
        /// An optional maximum expected duration for the operation.
        expected_max: Option<Duration>,
    },

    /// Resource exhaustion with precise metrics.
    ///
    /// This indicates that a system resource (e.g., memory, CPU, disk space,
    /// connection pool) has been exhausted. Includes current usage, limits,
    /// and percentage for monitoring and alerting.
    ///
    /// # Fields
    ///
    /// * `resource` - Type of resource exhausted
    /// * `limit` - Configured limit for the resource
    /// * `current` - Current usage when exhaustion occurred
    /// * `usage_percentage` - Optional percentage for easy monitoring
    ResourceExhausted {
        /// The type of resource exhausted (e.g., "memory", "thread pool").
        resource: Arc<str>,
        /// The configured limit for the resource.
        limit: Arc<str>,
        /// The current usage or allocation of the resource when exhaustion occurred.
        current: Arc<str>,
        /// Optional percentage of resource usage at the time of error.
        usage_percentage: Option<f64>,
    },

    /// Security-related error with enhanced threat classification.
    ///
    /// This variant represents security violations, authentication failures,
    /// authorization denials, and other security-related issues that require
    /// special handling and potential security response.
    ///
    /// # Fields
    ///
    /// * `message` - Description of the security error
    /// * `source` - Optional nested error that caused this security issue
    /// * `security_level` - Classification of the security threat level
    Security {
        /// A human-readable description of the security error.
        message: Arc<str>,
        /// An optional nested [`Yoshi`] error that caused this security issue.
        source: Option<Box<Yoshi>>,
        /// Classification of the security threat level.
        security_level: Arc<str>,
    },

    /// Foreign error wrapper with enhanced type information.
    ///
    /// This variant allows wrapping any type that implements `std::error::Error`,
    /// providing a uniform way to integrate external error types into the Yoshi
    /// framework while preserving the original error information.
    ///
    /// # Fields
    ///
    /// * `error` - The boxed foreign error object
    /// * `error_type_name` - Fully qualified type name of the original error
    Foreign {
        /// The boxed foreign error object.
        error: Box<dyn Error + Send + Sync + 'static>,
        /// The fully qualified type name of the original error.
        error_type_name: Arc<str>,
    },

    /// Multiple errors with categorization and priority.
    ///
    /// This variant can be used to aggregate several errors into a single Yoshi
    /// instance, useful for scenarios like batch processing or validation where
    /// multiple failures can occur simultaneously.
    ///
    /// # Fields
    ///
    /// * `errors` - Vector of nested Yoshi errors
    /// * `primary_index` - Optional index indicating the most important error
    Multiple {
        /// A vector of nested [`Yoshi`] errors.
        errors: Vec<Yoshi>,
        /// An optional index indicating which error in the `errors`
        /// vector should be considered the primary error.
        primary_index: Option<usize>,
    },
}

impl YoshiKind {
    /// Gets the severity level of this error kind (0-100, higher is more severe).
    ///
    /// This method provides a numerical indication of how critical an error
    /// is, allowing for programmatic decision-making based on severity
    /// (e.g., logging level, alerting, retry behavior).
    ///
    /// # Returns
    ///
    /// A `u8` value representing the severity, where 0 is least severe
    /// and 100 is most severe.
    ///
    /// # Severity Scale
    ///
    /// - **0-25**: Informational (`NotFound`, `Validation`)
    /// - **26-50**: Warning (`Config`, `Network`, `Timeout`)
    /// - **51-75**: Error (`ResourceExhausted`, `Foreign`)
    /// - **76-100**: Critical (`Internal`, `Security`)
    #[must_use]
    pub const fn severity(&self) -> u8 {
        match self {
            Self::Io(_) => 40,
            #[cfg(feature = "std")]
            Self::StdIo(_) => 40,
            Self::Network { .. } => 50,
            Self::Config { .. } => 30,
            Self::Validation { .. } => 20,
            Self::Internal { .. } => 80,
            Self::NotFound { .. } => 25,
            Self::Timeout { .. } => 45,
            Self::ResourceExhausted { .. } => 70,
            Self::Security { .. } => 90, // High severity for security issues
            Self::Foreign { .. } => 60,
            Self::Multiple { .. } => 65,
        }
    }

    /// Checks if this error kind represents a transient (retryable) error.
    ///
    /// Transient errors are typically temporary issues that might resolve
    /// themselves if the operation is retried after a short delay (e.g.,
    /// network glitches, temporary resource unavailability).
    ///
    /// # Returns
    ///
    /// `true` if the error is considered transient, `false` otherwise.
    ///
    /// # Transient Error Types
    ///
    /// - **Network**: Connection issues, temporary service unavailability
    /// - **Timeout**: Operations that may succeed with more time
    /// - **`ResourceExhausted`**: Temporary resource constraints
    /// - **I/O**: Some I/O operations may succeed on retry
    #[must_use]
    pub const fn is_transient(&self) -> bool {
        matches!(
            self,
            Self::Network { .. }
                | Self::Timeout { .. }
                | Self::ResourceExhausted { .. }
                | Self::Io(_)
        ) || {
            #[cfg(feature = "std")]
            matches!(self, Self::StdIo(_))
            #[cfg(not(feature = "std"))]
            false
        }
    }
}

impl Display for YoshiKind {
    /// Minimal display implementation for core crate.
    fn fmt(&self, f: &mut Formatter<'_>) -> core::fmt::Result {
        match self {
            Self::Io(e) => write!(f, "I/O error: {e}"),
            #[cfg(feature = "std")]
            Self::StdIo(e) => write!(f, "I/O error: {e}"),
            Self::Network { message, .. } => write!(f, "Network error: {message}"),
            Self::Config { message, .. } => write!(f, "Configuration error: {message}"),
            Self::Validation { field, message, .. } => write!(f, "Validation error in {field}: {message}"),
            Self::Internal { message, .. } => write!(f, "Internal error: {message}"),
            Self::NotFound { resource_type, identifier, .. } => write!(f, "{resource_type} not found: {identifier}"),
            Self::Timeout { operation, .. } => write!(f, "Timeout: {operation}"),
            Self::ResourceExhausted { resource, .. } => write!(f, "Resource exhausted: {resource}"),
            Self::Security { message, .. } => write!(f, "Security error: {message}"),
            Self::Foreign { error_type_name, .. } => write!(f, "Foreign error: {error_type_name}"),
            Self::Multiple { errors, .. } => write!(f, "Multiple errors ({} total)", errors.len()),
        }
    }
}

impl Error for YoshiKind {
    fn source(&self) -> Option<&(dyn Error + 'static)> {
        match self {
            Self::Io(e) => Some(e),
            #[cfg(feature = "std")]
            Self::StdIo(e) => Some(e),
            Self::Network { source: Some(s), .. }
            | Self::Config { source: Some(s), .. }
            | Self::Internal { source: Some(s), .. }
            | Self::Security { source: Some(s), .. } => Some(s.as_ref()),
            Self::Foreign { error, .. } => Some(error.as_ref()),
            Self::Multiple { errors, primary_index } => {
                if let Some(idx) = primary_index {
                    errors.get(*idx).map(|e| e as &dyn Error)
                } else {
                    errors.first().map(|e| e as &dyn Error)
                }
            }
            _ => None,
        }
    }
}

//============================================================================
// SECTION 5: NO_STD I/O ERROR IMPLEMENTATION
//============================================================================

/// Structured error kinds for better type safety in no_std I/O operations.
///
/// This enum provides a categorized approach to I/O errors in environments
/// where `std::io::Error` is not available. Each variant represents a common
/// class of I/O errors with clear semantics.
///
/// # Examples
///
/// ```rust
/// use yoshi_core::NoStdIoKind;
///
/// let kind = NoStdIoKind::NotFound;
/// assert_eq!(kind.as_str(), "not found");
/// assert!(!kind.is_transient());
/// ```
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash)]
pub enum NoStdIoKind {
    /// A file or directory was not found.
    NotFound,
    /// Permission was denied for the operation.
    PermissionDenied,
    /// A network connection was refused.
    ConnectionRefused,
    /// An operation timed out.
    TimedOut,
    /// A generic I/O error occurred.
    Generic,
    /// Other error types not covered by specific variants.
    Other,
}

impl NoStdIoKind {
    /// Returns a human-readable description of the error kind.
    ///
    /// # Examples
    ///
    /// ```rust
    /// use yoshi_core::NoStdIoKind;
    ///
    /// assert_eq!(NoStdIoKind::NotFound.as_str(), "not found");
    /// assert_eq!(NoStdIoKind::PermissionDenied.as_str(), "permission denied");
    /// ```
    pub const fn as_str(&self) -> &'static str {
        match self {
            Self::NotFound => "not found",
            Self::PermissionDenied => "permission denied",
            Self::ConnectionRefused => "connection refused",
            Self::TimedOut => "timed out",
            Self::Generic => "I/O error",
            Self::Other => "other error",
        }
    }

    /// Returns whether this error kind typically indicates a transient condition.
    ///
    /// Transient errors are those that might succeed if retried after a delay.
    ///
    /// # Examples
    ///
    /// ```rust
    /// use yoshi_core::NoStdIoKind;
    ///
    /// assert!(NoStdIoKind::TimedOut.is_transient());
    /// assert!(!NoStdIoKind::NotFound.is_transient());
    /// ```
    pub const fn is_transient(&self) -> bool {
        matches!(
            self,
            Self::ConnectionRefused | Self::TimedOut | Self::Generic
        )
    }

    /// Returns a severity level for this error kind (0-100).
    ///
    /// Higher values indicate more severe errors that require immediate attention.
    pub const fn severity(&self) -> u8 {
        match self {
            Self::NotFound => 30,
            Self::PermissionDenied => 50,
            Self::ConnectionRefused => 40,
            Self::TimedOut => 35,
            Self::Generic => 45,
            Self::Other => 40,
        }
    }
}

impl Display for NoStdIoKind {
    fn fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result {
        f.write_str(self.as_str())
    }
}

/// High-performance minimal wrapper for I/O errors in `no_std` contexts.
///
/// This enum provides a compact representation for common I/O errors
/// when the standard library's `std::io::Error` is not available.
/// It uses `Arc<str>` for message storage to minimize allocations
/// when messages are repeated or shared.
///
/// # Performance Characteristics
///
/// - **Memory Efficient**: Uses `Arc<str>` for shared error messages
/// - **Pattern Recognition**: Automatically categorizes errors from messages
/// - **No Allocations**: Static variants for common error types
/// - **Thread Safe**: All variants are `Send + Sync`
///
/// # Examples
///
/// ```rust
/// use yoshi_core::NoStdIo;
///
/// // Automatic categorization from message
/// let error = NoStdIo::new("file not found");
/// assert!(matches!(error, NoStdIo::NotFound));
///
/// // Custom error with message
/// let custom = NoStdIo::new("disk full");
/// assert!(matches!(custom, NoStdIo::Other(_)));
/// ```
#[derive(Debug, Clone)]
pub enum NoStdIo {
    /// Generic I/O error with optimized string storage.
    GenericIo(Arc<str>),
    /// Indicates that a file or directory was not found.
    NotFound,
    /// Indicates that permission was denied for an operation.
    PermissionDenied,
    /// Indicates that a network connection was refused.
    ConnectionRefused,
    /// Indicates that an operation timed out.
    TimedOut,
    /// Other I/O errors, with a custom message.
    Other(Arc<str>),
}

impl NoStdIo {
    /// Creates a new I/O error with comprehensive categorization.
    ///
    /// This constructor attempts to categorize the error message into specific
    /// variants using pattern matching on common error strings, enabling
    /// better programmatic error handling even in no_std environments.
    ///
    /// # Arguments
    ///
    /// * `message` - A message describing the I/O error. This can be any type
    ///   that converts into a `String`.
    ///
    /// # Returns
    ///
    /// A new `NoStdIo` error instance, automatically categorized based on the message.
    ///
    /// # Pattern Recognition
    ///
    /// The function recognizes common error patterns:
    /// - "not found", "no such file", "enoent" → `NotFound`
    /// - "permission denied", "access denied", "eacces" → `PermissionDenied`
    /// - "connection refused", "econnrefused" → `ConnectionRefused`
    /// - "timed out", "timeout", "etimedout" → `TimedOut`
    /// - "i/o error", "input/output error" → `GenericIo`
    /// - Everything else → `Other`
    pub fn new(message: impl Into<String>) -> Self {
        let msg = message.into();
        let lower_msg = msg.to_lowercase();

        // Comprehensive pattern matching for better error categorization
        match lower_msg.as_str() {
            // File/resource not found patterns
            s if s.contains("not found")
                || s.contains("no such file")
                || s.contains("enoent")
                || s.contains("file does not exist") =>
            {
                Self::NotFound
            }

            // Permission/access denied patterns
            s if s.contains("permission denied")
                || s.contains("access denied")
                || s.contains("access is denied")
                || s.contains("eacces")
                || s.contains("unauthorized")
                || s.contains("forbidden") =>
            {
                Self::PermissionDenied
            }

            // Network connection patterns
            s if s.contains("connection refused")
                || s.contains("econnrefused")
                || s.contains("no route to host")
                || s.contains("network unreachable") =>
            {
                Self::ConnectionRefused
            }

            // Timeout patterns
            s if s.contains("timed out")
                || s.contains("timeout")
                || s.contains("etimedout")
                || s.contains("operation timeout") =>
            {
                Self::TimedOut
            }

            // Generic I/O patterns
            s if s.contains("i/o error")
                || s.contains("io error")
                || s.contains("input/output error") =>
            {
                Self::GenericIo(msg.into())
            }

            // Catch-all for unrecognized patterns
            _ => Self::Other(msg.into()),
        }
    }

    /// Creates a new I/O error from an error code and message.
    ///
    /// This method provides more precise error categorization when
    /// both an error code and message are available, such as when
    /// wrapping system call errors.
    ///
    /// # Arguments
    ///
    /// * `code` - The numeric error code (e.g., errno values)
    /// * `message` - Descriptive message for the error
    ///
    /// # Error Code Mapping
    ///
    /// Common error codes are mapped to specific variants:
    /// - `2` or `-2` (ENOENT) → `NotFound`
    /// - `13` or `-13` (EACCES) → `PermissionDenied`
    /// - `111` or `-111` (ECONNREFUSED) → `ConnectionRefused`
    /// - `110` or `-110` (ETIMEDOUT) → `TimedOut`
    /// - `5` or `-5` (EIO) → `GenericIo`
    /// - Other codes → `Other`
    pub fn from_code_and_message(code: i32, message: impl Into<String>) -> Self {
        match code {
            2 | -2 => Self::NotFound,                         // ENOENT
            13 | -13 => Self::PermissionDenied,               // EACCES
            111 | -111 => Self::ConnectionRefused,            // ECONNREFUSED
            110 | -110 => Self::TimedOut,                     // ETIMEDOUT
            5 | -5 => Self::GenericIo(message.into().into()), // EIO
            _ => Self::Other(message.into().into()),
        }
    }

    /// Creates a typed I/O error for specific common scenarios.
    ///
    /// This method allows direct creation of specific error variants
    /// when the error type is known in advance, bypassing pattern recognition.
    ///
    /// # Arguments
    ///
    /// * `error_type` - The specific type of I/O error
    /// * `message` - Descriptive message for the error
    pub fn typed_error(error_type: NoStdIoKind, message: impl Into<String>) -> Self {
        match error_type {
            NoStdIoKind::NotFound => Self::NotFound,
            NoStdIoKind::PermissionDenied => Self::PermissionDenied,
            NoStdIoKind::ConnectionRefused => Self::ConnectionRefused,
            NoStdIoKind::TimedOut => Self::TimedOut,
            NoStdIoKind::Generic => Self::GenericIo(message.into().into()),
            NoStdIoKind::Other => Self::Other(message.into().into()),
        }
    }
}

impl Display for NoStdIo {
    fn fmt(&self, f: &mut Formatter<'_>) -> core::fmt::Result {
        match self {
            Self::GenericIo(s) => write!(f, "I/O error (no_std): {s}"),
            Self::NotFound => f.write_str("I/O error (no_std): not found"),
            Self::PermissionDenied => f.write_str("I/O error (no_std): permission denied"),
            Self::ConnectionRefused => f.write_str("I/O error (no_std): connection refused"),
            Self::TimedOut => f.write_str("I/O error (no_std): timed out"),
            Self::Other(s) => write!(f, "I/O error (no_std): {s}"),
        }
    }
}

impl Error for NoStdIo {}

//============================================================================
// SECTION 6: CONTEXT AND LOCATION SYSTEM
//============================================================================

/// Enhanced source code location with const evaluation.
///
/// `YoshiLocation` captures the file name, line number, and column number
/// where an error or context was created. This is crucial for debugging
/// and pinpointing the exact origin of an issue in the source code.
///
/// # Performance
///
/// - **Compile-time Construction**: Uses `const` evaluation where possible
/// - **Zero Runtime Cost**: Location capture has no runtime overhead
/// - **Static Storage**: File paths are stored as static string slices
///
/// # Examples
///
/// ```rust
/// use yoshi_core::{YoshiLocation, yoshi_location};
///
/// // Manual location creation
/// let loc = YoshiLocation::new("src/main.rs", 42, 8);
/// assert_eq!(loc.filename(), "main.rs");
///
/// // Automatic location capture via macro
/// let current_loc = yoshi_location!();
/// println!("Error at: {}", current_loc);
/// ```
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash)]
#[cfg_attr(feature = "serde", derive(serde::Serialize, serde::Deserialize))]
#[cfg_attr(feature = "serde", serde(bound(serialize = "", deserialize = "")))]
#[cfg_attr(docsrs, doc(cfg(feature = "serde")))]
pub struct YoshiLocation {
    /// File path with const string optimization.
    ///
    /// A static string slice representing the full path to the source file.
    /// This is typically populated by the `file!()` macro.
    pub file: &'static str,
    /// Line number.
    ///
    /// The line number in the source file (1-based).
    pub line: u32,
    /// Column number.
    ///
    /// The column number within the line in the source file (1-based).
    pub column: u32,
}

impl YoshiLocation {
    /// Creates a new location with const evaluation where possible.
    ///
    /// This constructor is typically used by the [`yoshi_location!`] macro
    /// to capture source locations at compile time.
    ///
    /// # Arguments
    ///
    /// * `file` - The static string slice representing the file path
    /// * `line` - The line number (1-based)
    /// * `column` - The column number (1-based)
    ///
    /// # Examples
    ///
    /// ```rust
    /// use yoshi_core::YoshiLocation;
    ///
    /// const MY_LOCATION: YoshiLocation = YoshiLocation::new("src/main.rs", 10, 5);
    /// assert_eq!(MY_LOCATION.file, "src/main.rs");
    /// assert_eq!(MY_LOCATION.line, 10);
    /// assert_eq!(MY_LOCATION.column, 5);
    /// ```
    #[inline]
    #[must_use]
    pub const fn new(file: &'static str, line: u32, column: u32) -> Self {
        Self { file, line, column }
    }

    /// Gets just the filename without path for compact display.
    ///
    /// This utility method extracts the base filename from the full
    /// file path, making it more convenient for display in logs or
    /// error messages where the full path might be too verbose.
    ///
    /// # Returns
    ///
    /// A string slice containing only the filename.
    ///
    /// # Examples
    ///
    /// ```rust
    /// use yoshi_core::YoshiLocation;
    ///
    /// let loc = YoshiLocation::new("/home/<USER>/project/src/lib.rs", 1, 1);
    /// assert_eq!(loc.filename(), "lib.rs");
    ///
    /// let loc_windows = YoshiLocation::new("C:\\Users\\<USER>\\main.rs", 1, 1);
    /// // Works with both Unix and Windows path separators
    /// assert!(loc_windows.filename().ends_with("main.rs"));
    /// ```
    #[inline]
    #[must_use]
    pub fn filename(&self) -> &str {
        self.file.rsplit('/').next().unwrap_or(self.file)
    }
}

impl Display for YoshiLocation {
    /// Formats the `YoshiLocation` for display in `file:line:column` format.
    ///
    /// Uses only the filename (not full path) for compact display that's
    /// suitable for terminal output and log files.
    ///
    /// # Examples
    ///
    /// ```rust
    /// use yoshi_core::YoshiLocation;
    ///
    /// let loc = YoshiLocation::new("src/utils.rs", 123, 45);
    /// assert_eq!(format!("{}", loc), "utils.rs:123:45");
    /// ```
    #[inline]
    fn fmt(&self, f: &mut Formatter<'_>) -> core::fmt::Result {
        write!(f, "{}:{}:{}", self.filename(), self.line, self.column)
    }
}

/// Optimized macro for location capture with const evaluation.
///
/// This macro expands to a [`YoshiLocation`] instance containing the file path,
/// line number, and column number where it was invoked. It uses `core::file!`,
/// `core::line!`, and `core::column!` for compile-time capture.
///
/// # Returns
///
/// A `YoshiLocation` struct representing the call site.
///
/// # Examples
///
/// ```rust
/// use yoshi_core::yoshi_location;
///
/// let loc = yoshi_location!();
/// // The file, line, and column correspond to where yoshi_location!() was called
/// println!("Error occurred at: {}", loc);
/// assert!(loc.line > 0);
/// assert!(loc.column > 0);
/// ```
///
/// # Performance
///
/// This macro has zero runtime cost - all location information is captured
/// at compile time and embedded as constants in the binary.
#[macro_export]
macro_rules! yoshi_location {
    () => {
        $crate::YoshiLocation::new(core::file!(), core::line!(), core::column!())
    };
}

/// Detailed context analysis results
///
/// This struct provides comprehensive metrics about the contextual information
/// attached to a `Yoshi` error, useful for analysis, logging, and debugging.
#[derive(Debug, Default)]
pub struct ContextAnalysis {
    /// Total number of context objects attached to the error
    pub total_contexts: usize,
    /// Maximum depth of nested context information
    pub context_depth: usize,
    /// Whether the error includes user-facing suggestions
    pub has_suggestions: bool,
    /// Whether source code location information is available
    pub has_location_info: bool,
    /// Number of metadata key-value pairs attached
    pub metadata_entries: usize,
    /// Number of typed shell objects attached
    pub typed_payloads: usize,
    /// Priority level of the primary context (0-255)
    pub primary_context_priority: u8,
}

/// Enhanced structured context with performance optimizations and type safety.
///
/// `YoContext` provides additional, application-specific information
/// about an error that helps in debugging, logging, and recovery.
/// It supports messages, metadata, suggestions, and arbitrary typed payloads.
///
/// # Performance Characteristics
///
/// - **String Interning**: Automatic deduplication of repeated context messages
/// - **Shared Storage**: Uses `Arc<str>` for efficient memory sharing
/// - **Bounded Payloads**: Limits shell count to prevent memory exhaustion
/// - **Fast Access**: O(1) metadata lookup via `HashMap`
///
/// # Examples
///
/// ```rust
/// use yoshi_core::YoContext;
///
/// let ctx = YoContext::new("Processing user request")
///     .with_metadata("user_id", "12345")
///     .with_metadata("session_id", "abcde")
///     .with_suggestion("Retry with exponential backoff")
///     .with_priority(200);
///
/// assert_eq!(ctx.message.as_deref(), Some("Processing user request"));
/// assert_eq!(ctx.priority, 200);
/// ```
#[derive(Debug, Default, Clone)]
#[cfg_attr(feature = "serde", derive(serde::Serialize, serde::Deserialize))]
#[cfg_attr(feature = "serde", serde(bound(serialize = "", deserialize = "")))]
#[cfg_attr(docsrs, doc(cfg(feature = "serde")))]
pub struct YoContext {
    /// Main message with Arc optimization for shared contexts.
    ///
    /// This field holds a descriptive message for the context. Using `Arc<str>`
    /// allows efficient sharing when the same context message is used in multiple places.
    #[cfg_attr(
        feature = "serde",
        serde(
            serialize_with = "serialize_arc_str",
            deserialize_with = "deserialize_arc_str"
        )
    )]
    pub message: Option<Arc<str>>,

    /// Metadata with optimized storage for common keys.
    ///
    /// A hash map storing key-value pairs of additional diagnostic information.
    /// Keys and values are `Arc<str>` for efficient sharing across contexts.
    #[cfg_attr(
        feature = "serde",
        serde(
            default,
            serialize_with = "serialize_arc_str_map",
            deserialize_with = "deserialize_arc_str_map"
        )
    )]
    pub metadata: HashMap<Arc<str>, Arc<str>>,

    /// Recovery suggestion with shared storage.
    ///
    /// An optional human-readable suggestion for how to resolve or work around the error.
    /// Using `Arc<str>` allows efficient sharing of common suggestions.
    #[cfg_attr(
        feature = "serde",
        serde(
            serialize_with = "serialize_arc_str",
            deserialize_with = "deserialize_arc_str"
        )
    )]
    pub suggestion: Option<Arc<str>>,

    /// Source location with compile-time capture.
    ///
    /// An optional [`YoshiLocation`] indicating where this context was added in the source code.
    /// This is automatically populated when using the [`yoshi_location!`] macro.
    #[cfg_attr(feature = "serde", serde(skip))]
    pub location: Option<YoshiLocation>,

    /// Typed payloads with optimized storage.
    ///
    /// A vector of arbitrary `Any + Send + Sync + 'static` types, allowing for
    /// rich, structured data to be attached to an error. Shells are shared
    /// across cloned contexts via `Arc` to ensure memory efficiency.
    #[cfg_attr(feature = "serde", serde(skip))]
    pub payloads: Vec<Arc<Box<dyn Any + Send + Sync + 'static>>>,

    /// Context creation timestamp for debugging.
    ///
    /// An optional `SystemTime` indicating when this context was created.
    /// Useful for understanding error timeline and performance analysis.
    #[cfg_attr(feature = "serde", serde(skip))]
    pub created_at: Option<SystemTime>,

    /// Context priority for error handling (0-255, higher is more important).
    ///
    /// A numerical value indicating the importance or relevance of this context
    /// relative to other contexts attached to the same error. Used for filtering
    /// and prioritizing context information in logs and error displays.
    pub priority: u8,
}

impl YoContext {
    /// Creates a new context with optimized string allocation.
    ///
    /// This is the primary way to create a new `YoContext`. It automatically
    /// captures the current system time and sets a default priority.
    /// Uses string interning for memory efficiency.
    ///
    /// # Arguments
    ///
    /// * `msg` - The main message for this context. It can be any type
    ///   that converts into a `String`.
    ///
    /// # Returns
    ///
    /// A new `YoContext` instance with the message set and timestamp captured.
    ///
    /// # Examples
    ///
    /// ```rust
    /// use yoshi_core::YoContext;
    ///
    /// let ctx = YoContext::new("Attempting to connect to database");
    /// assert_eq!(ctx.message.as_deref(), Some("Attempting to connect to database"));
    /// assert!(ctx.created_at.is_some());
    /// assert_eq!(ctx.priority, 128); // Default medium priority
    /// ```
    #[inline]
    pub fn new(msg: impl Into<String>) -> Self {
        Self {
            message: Some(msg.into().into()),
            created_at: Some(SystemTime::now()),
            priority: 128, // Default medium priority
            ..Self::default()
        }
    }

    /// Adds metadata with optimized string interning.
    ///
    /// This method allows attaching arbitrary key-value metadata to the context.
    /// It consumes `self` and returns a modified `Self`, enabling method chaining.
    /// Both keys and values are automatically interned for memory efficiency.
    ///
    /// # Arguments
    ///
    /// * `k` - The key for the metadata, convertible to `String`
    /// * `v` - The value for the metadata, convertible to `String`
    ///
    /// # Returns
    ///
    /// The `YoContext` instance with the new metadata added.
    #[must_use]
    #[inline]
    pub fn with_metadata(mut self, k: impl Into<String>, v: impl Into<String>) -> Self {
        self.metadata
            .insert(k.into().into(), v.into().into());
        self
    }

    /// Adds a suggestion with shared storage optimization.
    ///
    /// This method attaches a human-readable suggestion to the context,
    /// guiding users or operators on how to resolve the error. It consumes
    /// `self` and returns a modified `Self`.
    ///
    /// # Arguments
    ///
    /// * `s` - The suggestion message, convertible to `String`
    ///
    /// # Returns
    ///
    /// The `YoContext` instance with the suggestion added.
    #[must_use]
    #[inline]
    pub fn with_suggestion(mut self, s: impl Into<String>) -> Self {
        self.suggestion = Some(s.into().into());
        self
    }

    /// Attaches a typed shell with enhanced type safety.
    ///
    /// This method allows attaching typed payloads with better type tracking
    /// for safer retrieval and debugging. Each shell is tagged with its type name.
    /// The shell count is bounded to prevent memory exhaustion.
    ///
    /// # Arguments
    ///
    /// * `shell` - The data to attach. It must implement `Any`, `Send`, `Sync`, and have a `'static` lifetime.
    ///
    /// # Returns
    ///
    /// The `YoContext` instance with the shell added.
    #[must_use]
    #[inline]
    pub fn with_shell(mut self, shell: impl Any + Send + Sync + 'static) -> Self {
        // Limit shell count to prevent memory exhaustion
        const MAX_PAYLOADS: usize = 16;
        if self.payloads.len() < MAX_PAYLOADS {
            // Store as Arc<Box<dyn Any>> to enable cloning of the Vec<Arc<...>>
            self.payloads.push(Arc::new(Box::new(shell)));
        }
        self
    }

    /// Sets the priority level for this context.
    ///
    /// The priority helps in ordering and selecting the most relevant contexts
    /// when an error is formatted or processed. Higher values indicate higher priority.
    ///
    /// # Arguments
    ///
    /// * `priority` - The priority level, a `u8` value from 0 to 255.
    ///
    /// # Returns
    ///
    /// The `YoContext` instance with the updated priority.
    #[must_use]
    #[inline]
    pub fn with_priority(mut self, priority: u8) -> Self {
        self.priority = priority;
        self
    }

    /// Sets location information on this context.
    ///
    /// This method attaches source code location information to the context,
    /// helping with debugging and error tracing. It consumes `self` and
    /// returns a modified `Self`.
    ///
    /// # Arguments
    ///
    /// * `location` - The `YoshiLocation` to set.
    ///
    /// # Returns
    ///
    /// The `YoContext` instance with the location set.
    #[must_use]
    #[inline]
    pub fn with_location(mut self, location: YoshiLocation) -> Self {
        self.location = Some(location);
        self
    }
}

//============================================================================
// SECTION 7: BACKTRACE SYSTEM (NO_STD)
//============================================================================

/// Minimal backtrace information for `no_std` environments.
///
/// While full stack traces aren't available without std, this provides
/// basic debugging information that can be useful for error correlation
/// and basic debugging in embedded/no_std environments.
///
/// # Performance Characteristics
///
/// - **Memory Usage**: ~100-500 bytes depending on location count
/// - **Capture Cost**: <1μs (just location and timestamp capture)
/// - **Thread Safety**: Safe for concurrent access
/// - **Storage**: Efficient vector storage with bounded growth
///
/// # Examples
///
/// ```rust
/// use yoshi_core::{YoshiBacktrace, yoshi_location};
///
/// let bt = YoshiBacktrace::new_captured();
/// println!("Backtrace depth: {}", bt.call_depth());
///
/// if let Some(top) = bt.top_location() {
///     println!("Error location: {}", top);
/// }
/// ```
#[derive(Debug, Clone)]
pub struct YoshiBacktrace {
    /// Source locations captured during error propagation
    pub locations: Vec<YoshiLocation>,
    /// Timestamp when backtrace was captured
    pub capture_timestamp: SystemTime,
    /// Thread ID where backtrace was captured
    pub thread_id: ThreadId,
    /// Simple call depth indicator
    pub call_depth: u32,
}

impl YoshiBacktrace {
    /// Creates a new minimal backtrace for no_std environments.
    ///
    /// Automatically captures the current location using the `yoshi_location!` macro.
    pub fn new_captured() -> Self {
        Self::new_with_location(yoshi_location!())
    }

    /// Creates a backtrace with a specific source location.
    ///
    /// This is useful when you want to capture a backtrace at a specific
    /// location other than where the backtrace object is created.
    ///
    /// # Arguments
    ///
    /// * `location` - The `YoshiLocation` to use as the initial location
    pub fn new_with_location(location: YoshiLocation) -> Self {
        let mut locations = Vec::new();
        locations.push(location);
        Self {
            locations,
            capture_timestamp: SystemTime::now(),
            thread_id: ThreadId::current(),
            call_depth: 1,
        }
    }

    /// Adds a location to the backtrace chain.
    ///
    /// This can be used to manually build up a call chain as errors
    /// propagate through the system.
    ///
    /// # Arguments
    ///
    /// * `location` - The `YoshiLocation` to add to the trace
    pub fn add_location(&mut self, location: YoshiLocation) {
        self.locations.push(location);
        self.call_depth += 1;
    }

    /// Returns the call depth.
    ///
    /// This indicates how many locations have been added to the backtrace.
    pub const fn call_depth(&self) -> u32 {
        self.call_depth
    }

    /// Returns the capture timestamp.
    pub const fn capture_timestamp(&self) -> SystemTime {
        self.capture_timestamp
    }

    /// Returns the thread ID where this was captured.
    pub const fn thread_id(&self) -> ThreadId {
        self.thread_id
    }

    /// Returns an iterator over the captured locations.
    ///
    /// Locations are ordered from first captured (bottom of stack) to
    /// most recent (top of stack).
    pub fn locations(&self) -> impl Iterator<Item = &YoshiLocation> {
        self.locations.iter()
    }

    /// Returns the most recent (top) location in the backtrace.
    ///
    /// This is typically the most relevant location for debugging purposes.
    pub fn top_location(&self) -> Option<&YoshiLocation> {
        self.locations.last()
    }

    /// Returns a status indicating the backtrace state.
    ///
    /// This provides compatibility with the std backtrace status API.
    pub fn status(&self) -> BacktraceStatus {
        if self.locations.is_empty() {
            BacktraceStatus::Disabled
        } else {
            BacktraceStatus::Captured
        }
    }
}

impl Display for YoshiBacktrace {
    fn fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result {
        writeln!(
            f,
            "Minimal backtrace (no_std) captured at: {:?}",
            self.capture_timestamp
        )?;
        writeln!(
            f,
            "Thread: {} | Call depth: {}",
            self.thread_id, self.call_depth
        )?;
        writeln!(f, "Locations:")?;

        for (i, location) in self.locations.iter().enumerate() {
            writeln!(f, "  {}: {}", i, location)?;
        }

        Ok(())
    }
}

/// Backtrace status for no_std environments.
///
/// Provides compatibility with the std library's `BacktraceStatus` enum
/// for unified API across std and no_std environments.
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum BacktraceStatus {
    /// Backtrace was captured successfully.
    Captured,
    /// Backtrace capture was disabled.
    Disabled,
    /// Backtrace capture is not supported.
    Unsupported,
}

//============================================================================
// SECTION 8: CORE YOSHI ERROR STRUCTURE (DEFINITION ONLY)
//============================================================================

/// The main `Yoshi` error type with enterprise-grade performance optimization.
///
/// `Yoshi` is a highly structured and extensible error type designed for
/// complex applications. It combines a categorized error kind, contextual
/// information, and optional backtrace capture into a single, cohesive unit.
///
/// # Architecture
///
/// - **`kind`**: The primary error classification via [`YoshiKind`]
/// - **`backtrace`**: Optional stack trace for debugging (feature-gated)
/// - **`contexts`**: Rich contextual information via [`YoContext`] chain
/// - **`instance_id`**: Unique identifier for error tracking and correlation
/// - **`created_at`**: Creation timestamp for debugging and analysis
///
/// # Performance Characteristics
///
/// - **Creation**: O(1) with pre-allocated context vectors
/// - **Context Addition**: O(1) amortized with vector growth
/// - **Memory**: Optimized with `Arc<str>` sharing and string interning
/// - **Thread Safety**: Full `Send + Sync` support for concurrent environments
///
/// # Note
///
/// This is the **definition only**. All implementation logic (methods, traits)
/// is provided by the `yoshi-std` crate. This separation ensures API stability
/// and allows for modular dependency management.
///
/// # Examples
///
/// ```rust
/// use yoshi_core::{Yoshi, YoshiKind};
///
/// let err = Yoshi::new(YoshiKind::Internal {
///     message: "Database connection failed".into(),
///     source: None,
///     component: Some("user_service".into()),
/// });
///
/// println!("Error {}: {}", err.instance_id, err);
/// ```
#[derive(Debug, Clone)]
pub struct Yoshi {
    /// The underlying error kind providing structured classification.
    pub kind: YoshiKind,

    /// Optional backtrace for debugging (no_std compatible).
    pub backtrace: Option<YoshiBacktrace>,

    /// Contexts providing additional information about the error.
    pub contexts: Vec<YoContext>,

    /// A unique identifier for this error instance.
    pub instance_id: u32,

    /// Timestamp when the error was created.
    pub created_at: SystemTime,
}

impl Yoshi {
    /// Creates a new `Yoshi` error with optimized allocation and monitoring.
    ///
    /// This is the **minimal constructor** provided in the core crate.
    /// All other functionality is implemented in `yoshi-std`.
    ///
    /// # Arguments
    ///
    /// * `kind` - The [`YoshiKind`] that categorizes this error.
    ///
    /// # Returns
    ///
    /// A new `Yoshi` error instance with unique ID and optional backtrace.
    #[inline]
    #[track_caller]
    pub fn new(kind: YoshiKind) -> Self {
        let instance_id = ERROR_INSTANCE_COUNTER.fetch_add(1, Ordering::Relaxed);

        Self {
            kind,
            backtrace: Some(YoshiBacktrace::new_captured()),
            contexts: Vec::with_capacity(4), // Pre-allocate for typical error chain depth
            instance_id,
            created_at: SystemTime::now(),
        }
    }
}

impl Display for Yoshi {
    /// Minimal display implementation for core crate.
    ///
    /// Enhanced display logic is provided by `yoshi-std`.
    fn fmt(&self, f: &mut Formatter<'_>) -> fmt::Result {
        write!(f, "{}", self.kind) // Use Display for YoshiKind in core
    }
}

impl Error for Yoshi {
    fn source(&self) -> Option<&(dyn Error + 'static)> {
        self.kind.source()
    }
}

//============================================================================
// SECTION 9: THEMATIC TYPE ALIASES AND TRAIT DEFINITIONS
//============================================================================

/// Performance-optimized Result alias with mathematical precision guarantees.
///
/// This type alias simplifies the use of `Result` where the error type is
/// fixed to [`Yoshi`]. It automatically adapts between `std::result::Result`
/// and `core::result::Result` based on the enabled features.
///
/// # Examples
///
/// ```rust
/// use yoshi_core::{Result, Yoshi, YoshiKind};
///
/// fn divide(a: f64, b: f64) -> Result<f64> {
///     if b == 0.0 {
///         return Err(Yoshi::new(YoshiKind::Validation {
///             field: "divisor".into(),
///             message: "Division by zero is not allowed".into(),
///             expected: Some("non-zero".into()),
///             actual: Some("0.0".into()),
///         }));
///     }
///     Ok(a / b)
/// }
/// ```
pub type Result<T, E = Yoshi> = core::result::Result<T, E>;

/// Ergonomic type alias for `Result<T, Yoshi>` with thematic naming (PRESERVED).
///
/// This type alias provides expressive naming that aligns with the Yoshi metaphorical
/// framework while maintaining zero-cost abstraction guarantees. The name "Hatch"
/// evokes the idea of operations that may "hatch" successfully or fail in the attempt.
///
/// # Performance Characteristics
///
/// - **Time Complexity**: O(1) for all operations (zero-cost abstraction)
/// - **Space Complexity**: Identical to `Result<T, Yoshi>` (no overhead)
/// - **Memory Layout**: Exact same representation as standard `Result`
///
/// # Examples
///
/// ```rust
/// use yoshi_core::{Hatch, Yoshi, YoshiKind};
///
/// fn load_config() -> Hatch<String> {
///     Ok("configuration data".into())
/// }
///
/// fn process_data() -> Hatch<u32> {
///     Err(Yoshi::new(YoshiKind::Internal {
///         message: "processing failed".into(),
///         source: None,
///         component: None,
///     }))
/// }
/// ```
pub type Hatch<T> = Result<T, Yoshi>;

/// Extension trait for mapping other `Result<T, E>` types into `Hatch<T>` easily.
///
/// This trait enables seamless integration between the Yoshi error ecosystem and
/// external error types. It provides the `.hatch()` method that converts any
/// `Result` with an error type that can be converted to `Yoshi` into a `Hatch<T>`.
///
/// # Note
///
/// This is a **trait definition only**. The implementation is provided by `yoshi-std`.
pub trait Hatchable<T, E> {
    /// Converts an error into a `Hatch<T>` by mapping it into `Yoshi`.
    ///
    /// This method provides a convenient way to bring external error types into
    /// the Yoshi ecosystem while maintaining type safety and performance efficiency.
    fn hatch(self) -> Hatch<T>;
}

/// Trait that adds `.lay(...)` to `Result<T, Yoshi>`, enriching errors with context (PRESERVED).
///
/// This trait provides ergonomic context attachment using thematic naming that
/// aligns with the Yoshi metaphorical framework. The `.lay()` method is equivalent
/// to adding context but uses intuitive, game-inspired terminology.
///
/// # Note
///
/// This is a **trait definition only**. The implementation is provided by `yoshi-std`.
pub trait LayText<T> {
    /// Adds a contextual message to the error chain, like laying an egg with metadata.
    ///
    /// This method enriches error information by attaching descriptive context
    /// that helps with debugging and error tracing. It uses thematic naming
    /// inspired by Yoshi's egg-laying ability to create memorable, intuitive APIs.
    fn lay(self, message: impl Into<String>) -> Hatch<T>;
}

/// Extension trait for `Result` to easily attach `Yoshi` context, suggestions, and metadata.
///
/// This trait provides convenience methods for `Result` types, allowing developers
/// to seamlessly add `YoContext`, suggestions, and metadata to errors as they
/// propagate through the application. This simplifies error handling chains and
/// ensures rich diagnostic information is preserved.
///
/// # Note
///
/// This is a **trait definition only**. The implementation is provided by `yoshi-std`.
pub trait HatchExt<T>
where
    Self: Sized,
{
    /// Adds a context message to the error.
    #[track_caller]
    fn context(self, msg: impl Into<String>) -> Hatch<T>;

    /// Adds a suggestion to the error's primary context.
    #[track_caller]
    fn with_suggestion(self, s: impl Into<String>) -> Hatch<T>;

    /// Attaches a typed shell to the error's primary context.
    #[track_caller]
    fn with_shell(self, p: impl Any + Send + Sync + 'static) -> Hatch<T>;

    /// Sets the priority for the error's primary context.
    #[track_caller]
    fn with_priority(self, priority: u8) -> Hatch<T>;

    /// Short alias for `context`.
    #[track_caller]
    fn ctx(self, msg: impl Into<String>) -> Hatch<T>;

    /// Short alias for `with_suggestion`.
    #[track_caller]
    fn help(self, s: impl Into<String>) -> Hatch<T>;

    /// Adds metadata to the error's primary context.
    #[track_caller]
    fn meta(self, k: impl Into<String>, v: impl Into<String>) -> Hatch<T>;
}

//============================================================================
// SECTION 10: MINIMAL CONVERSION IMPLEMENTATIONS
//============================================================================

impl From<String> for Yoshi {
    /// Converts a `String` into a `Yoshi` error.
    ///
    /// The string message is wrapped in an `Internal` `YoshiKind`.
    #[track_caller]
    fn from(s: String) -> Self {
        Yoshi::new(YoshiKind::Internal {
            message: s.into(),
            source: None,
            component: None,
        })
    }
}

impl From<&str> for Yoshi {
    /// Converts a string slice (`&str`) into a `Yoshi` error.
    ///
    /// The string slice is converted to a `String` and then wrapped in an
    /// `Internal` `YoshiKind`.
    #[track_caller]
    fn from(s: &str) -> Self {
        Yoshi::new(YoshiKind::Internal {
            message: s.to_string().into(),
            source: None,
            component: None,
        })
    }
}

impl From<NoStdIo> for Yoshi {
    /// Converts a `NoStdIo` error into a `Yoshi` error.
    ///
    /// The `NoStdIo` error is wrapped in a `YoshiKind::Io` variant.
    #[track_caller]
    fn from(e: NoStdIo) -> Self {
        Yoshi::new(YoshiKind::Io(e))
    }
}

//============================================================================
// SECTION 11: COMPREHENSIVE TEST SUITE
//============================================================================

#[cfg(test)]
mod tests {
    use super::*;

    // Helper to create a basic error for testing
    fn create_test_error() -> Yoshi {
        Yoshi::new(YoshiKind::Internal {
            message: "base error".into(),
            source: None,
            component: None,
        })
    }

    #[test]
    fn test_error_instance_counter_and_reset() {
        reset_error_instance_counter();
        assert_eq!(error_instance_count(), 0);

        let _err1 = create_test_error();
        assert_eq!(error_instance_count(), 1);

        let _err2 = create_test_error();
        assert_eq!(error_instance_count(), 2);

        reset_error_instance_counter();
        assert_eq!(error_instance_count(), 0);
    }

    #[test]
    fn test_basic_error_creation() {
        let err = create_test_error();
        assert!(err.instance_id > 0);
        assert!(matches!(err.kind, YoshiKind::Internal { .. }));
    }

    #[test]
    fn test_yoshi_location_macro() {
        let loc = yoshi_location!();
        assert!(loc.file.contains("lib.rs"));
        assert!(loc.line > 0);
        assert!(loc.column > 0);
    }

    #[test]
    fn test_system_time_ordering() {
        let t1 = SystemTime::now();
        let t2 = SystemTime::now();
        assert!(t2.timestamp() > t1.timestamp());
    }

    #[test]
    fn test_thread_id_uniqueness() {
        let id1 = ThreadId::current();
        let id2 = ThreadId::current();
        assert_ne!(id1, id2);
    }

    #[test]
    fn test_no_std_io_error_categorization() {
        let not_found = NoStdIo::new("file not found");
        assert!(matches!(not_found, NoStdIo::NotFound));

        let permission = NoStdIo::new("permission denied");
        assert!(matches!(permission, NoStdIo::PermissionDenied));

        let timeout = NoStdIo::new("operation timed out");
        assert!(matches!(timeout, NoStdIo::TimedOut));
    }

    #[test]
    fn test_yoshi_kind_severity() {
        let internal = YoshiKind::Internal {
            message: "test".into(),
            source: None,
            component: None,
        };
        assert_eq!(internal.severity(), 80);

        let validation = YoshiKind::Validation {
            field: "test".into(),
            message: "test".into(),
            expected: None,
            actual: None,
        };
        assert_eq!(validation.severity(), 20);
    }

    #[test]
    fn test_yoshi_kind_transient() {
        let network = YoshiKind::Network {
            message: "test".into(),
            source: None,
            error_code: None,
        };
        assert!(network.is_transient());

        let internal = YoshiKind::Internal {
            message: "test".into(),
            source: None,
            component: None,
        };
        assert!(!internal.is_transient());
    }

    #[test]
    fn test_yo_context_creation() {
        let ctx = YoContext::new("test context")
            .with_metadata("key", "value")
            .with_suggestion("try again")
            .with_priority(200);

        assert_eq!(ctx.message.as_deref(), Some("test context"));
        assert_eq!(ctx.metadata.get("key").unwrap().as_ref(), "value");
        assert_eq!(ctx.suggestion.as_deref(), Some("try again"));
        assert_eq!(ctx.priority, 200);
    }

    #[test]
    fn test_yoshi_backtrace() {
        let bt = YoshiBacktrace::new_captured();
        assert_eq!(bt.call_depth(), 1);
        assert!(bt.top_location().is_some());
        assert_eq!(bt.status(), BacktraceStatus::Captured);
    }

    #[test]
    fn test_conversions() {
        let str_error = Yoshi::from("string error");
        assert!(matches!(str_error.kind, YoshiKind::Internal { .. }));

        let string_error = Yoshi::from("string error".to_string());
        assert!(matches!(string_error.kind, YoshiKind::Internal { .. }));

        let io_error = Yoshi::from(NoStdIo::NotFound);
        assert!(matches!(io_error.kind, YoshiKind::Io(NoStdIo::NotFound)));
    }
}
