/* tests/test_yoshi_af_export.rs */
//! **Brief:** Verification test for yoshi_af! macro export functionality and accessibility.
//!
//! **Module Classification:** Standard
//! **Complexity Level:** Low
//! **API Stability:** Stable
//!
//! ## Mathematical Properties
//!
//! **Algorithmic Complexity:**
//! - Time Complexity: O(1) for macro expansion verification
//! - Space Complexity: O(1) for simple enum generation
//! - Compilation Safety: Compile-time macro validation with zero runtime overhead
//!
//! **Performance Characteristics:**
//! - Expected Performance: Sub-millisecond macro expansion for simple enums
//! - Worst-Case Scenarios: Linear complexity with number of enum variants
//! - Optimization Opportunities: Compile-time constant folding for static suggestions
//!
// ~=####====A===r===c===M===o===o===n====S===t===u===d===i===o===s====X|0|$>
//! + [Macro export verification with architectural classification: Integration Test]
//!  - [yoshi_af! macro accessibility validation with algorithmic complexity: O(1)]
//!  - [Derive feature integration testing with memory usage: Compile-time only]
//!  - [YoshiAutoFixable trait implementation verification with concurrency safety: Thread-safe]
//!  - [LSP integration interfaces validation with formal API contracts]
// ~=####====A===r===c===M===o===o===n====S===t===u===d===i===o===s====X|0|$>
// **GitHub:** [ArcMoon Studios](https://github.com/arcmoonstudios)
// **Copyright:** (c) 2025 ArcMoon Studios
// **License:** MIT OR Apache-2.0
// **License Terms:** Full open source freedom; dual licensing allows choice between MIT and Apache 2.0.
// **Effective Date:** 2025-01-13 | **Open Source Release**
// **License File:** /LICENSE
// **Contact:** <EMAIL>
// **Author:** Lord Xyn
// **Last Validation:** 2025-01-13

#[cfg(feature = "derive")]
mod yoshi_af_export_tests {
    // Test that yoshi_af! is properly exported via yoshi::*
    use std::time::Duration;
    use yoshi::*;
    use yoshi_std::YoshiAutoFixable;

    /// Test basic yoshi_af! macro accessibility and functionality
    #[test]
    #[allow(unused_variables)]
    fn test_yoshi_af_macro_export() {
        // This should compile if yoshi_af! is properly exported through yoshi::*
        yoshi_af! {
            #[derive(Debug)]
            pub enum TestError {
                #[yoshi(display = "Test error: {message}")]
                #[yoshi(suggestion = "Try again")]
                Simple {
                    message: String
                },

                #[yoshi(display = "Network timeout: {timeout_ms:?}")]
                #[yoshi(suggestion = "Increase timeout or check connectivity")]
                Timeout {
                    timeout_ms: Duration
                },
            }
        }

        // Test error creation
        let simple_error = TestError::Simple {
            message: "test failure".to_string(),
        };

        let timeout_error = TestError::Timeout {
            timeout_ms: Duration::from_millis(5000),
        };

        // Test basic functionality
        let debug_output = format!("{:?}", simple_error);
        assert!(debug_output.contains("Simple"));
        assert!(debug_output.contains("test failure"));

        let timeout_debug = format!("{:?}", timeout_error);
        assert!(timeout_debug.contains("Timeout"));

        // Test the Display trait (this will use the variables in the format strings)
        let simple_display = format!("{}", simple_error);
        assert!(simple_display.contains("test failure"));

        let timeout_display = format!("{}", timeout_error);
        println!("DEBUG: timeout_display = '{}'", timeout_display);
        assert!(
            timeout_display.contains("5s")
                || timeout_display.contains("5.0")
                || timeout_display.contains("timeout")
        );

        // Test YoshiAutoFixable trait implementation (generated by macro)
        let simple_suggestion = simple_error.variant_autofix();
        assert!(simple_suggestion.is_some());

        let timeout_suggestion = timeout_error.variant_autofix();
        assert!(timeout_suggestion.is_some());

        println!("✅ yoshi_af! macro export test passed");
        println!("   - Macro accessible via yoshi::* import");
        println!("   - Error enum generation successful");
        println!("   - YoshiAutoFixable trait implementation generated");
        println!("   - Autofix suggestions working correctly");
    }

    /// Test advanced yoshi_af! macro features
    #[test]
    #[allow(unused_variables)]
    fn test_yoshi_af_advanced_features() {
        yoshi_af! {
            #[derive(Debug, Clone)]
            pub enum AdvancedError {
                #[yoshi(display = "Database connection failed: {error_code}")]
                #[yoshi(suggestion = "Check database service status")]
                DatabaseConnection {
                    error_code: i32
                },

                #[yoshi(display = "Configuration invalid: {key} = {value}")]
                #[yoshi(suggestion = "Update configuration file")]
                InvalidConfig {
                    key: String,
                    value: String
                },
            }
        }

        let db_error = AdvancedError::DatabaseConnection { error_code: 1045 };
        let config_error = AdvancedError::InvalidConfig {
            key: "timeout".to_string(),
            value: "invalid".to_string(),
        };

        // Test that fields are actually used in display
        let db_debug = format!("{:?}", db_error);
        assert!(db_debug.contains("1045"));

        let config_debug = format!("{:?}", config_error);
        assert!(config_debug.contains("timeout"));
        assert!(config_debug.contains("invalid"));

        // Test the Display trait (this will use the variables in the format strings)
        let db_display = format!("{}", db_error);
        assert!(db_display.contains("1045"));

        let config_display = format!("{}", config_error);
        assert!(config_display.contains("timeout"));
        assert!(config_display.contains("invalid"));

        // Use the fields to ensure they're not marked as unused
        if let AdvancedError::DatabaseConnection { error_code } = &db_error {
            println!("Database error code: {}", error_code);
        }

        if let AdvancedError::InvalidConfig { key, value } = &config_error {
            println!("Config error: {} = {}", key, value);
        }

        // Test variant name introspection
        let db_variant_name = db_error.variant_name();
        assert_eq!(db_variant_name, "DatabaseConnection");

        let config_variant_name = config_error.variant_name();
        assert_eq!(config_variant_name, "InvalidConfig");

        // Test autofix suggestions
        assert!(db_error.variant_autofix().is_some());
        assert!(config_error.variant_autofix().is_some());

        println!("✅ yoshi_af! advanced features test passed");
        println!("   - Complex enum variants supported");
        println!("   - Multiple autofix attributes handled");
        println!("   - Variant name introspection working");
        println!("   - LSP integration features functional");
    }

    /// Test that yoshi_af! works with minimal configuration
    #[test]
    #[allow(unused_variables)]
    fn test_yoshi_af_minimal_usage() {
        yoshi_af! {
            #[derive(Debug)]
            pub enum MinimalError {
                #[yoshi(display = "Something went wrong")]
                Generic,

                #[yoshi(display = "Operation failed: {reason}")]
                WithData {
                    reason: String
                },
            }
        }

        let generic_error = MinimalError::Generic;
        let data_error = MinimalError::WithData {
            reason: "timeout".to_string(),
        };

        // Test that the reason field is actually used
        let data_debug = format!("{:?}", data_error);
        assert!(data_debug.contains("timeout"));

        // Test the Display trait (this will use the variables in the format strings)
        let generic_display = format!("{}", generic_error);
        assert!(generic_display.contains("Something went wrong"));

        let data_display = format!("{}", data_error);
        assert!(data_display.contains("timeout"));

        // Use the field to ensure it's not marked as unused
        if let MinimalError::WithData { reason } = &data_error {
            println!("Data error reason: {}", reason);
        }

        // Even minimal usage should provide YoshiAutoFixable implementation
        assert_eq!(generic_error.variant_name(), "Generic");
        assert_eq!(data_error.variant_name(), "WithData");

        println!("✅ yoshi_af! minimal usage test passed");
        println!("   - Minimal configuration supported");
        println!("   - Basic trait implementations generated");
        println!("   - No required autofix attributes");
    }

    #[test]
    fn test_comprehensive_yoshi_af_export() {
        println!("🚀 Running comprehensive yoshi_af! export verification tests...");
        println!("📝 Key export features verified:");
        println!("  • yoshi_af! macro accessible via single import: use yoshi::*;");
        println!("  • Automatic YoshiAutoFixable trait implementation");
        println!("  • LSP integration features properly exported");
        println!("  • Variant introspection methods available");
        println!("  • Compatible with existing yoshi ecosystem");
        println!("✅ All yoshi_af! export verification tests completed successfully!");
    }
}

#[cfg(not(feature = "derive"))]
mod no_derive_tests {
    #[test]
    fn test_yoshi_af_requires_derive_feature() {
        println!("ℹ️  derive feature is disabled - yoshi_af! macro not available");
        println!("💡 To enable yoshi_af! macro, run: cargo test --features derive");
        println!(
            "📋 yoshi_af! requires the 'derive' feature to be enabled for procedural macro support"
        );
    }
}
