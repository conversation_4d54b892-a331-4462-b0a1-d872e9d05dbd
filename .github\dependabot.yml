version: 2
updates:
  - package-ecosystem: "cargo"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "09:00"
    open-pull-requests-limit: 5
    target-branch: "develop"
    commit-message:
      prefix: "deps(cargo)"
      include: "scope"
    labels:
      - "dependencies"
      - "rust"
      - "auto-update"

  - package-ecosystem: "github-actions"
    directory: "/"
    schedule:
      interval: "monthly"
      day: "monday"
      time: "09:00"
    open-pull-requests-limit: 3
    target-branch: "develop"
    commit-message:
      prefix: "deps(actions)"
      include: "scope"
    labels:
      - "dependencies"
      - "github-actions"
      - "auto-update"