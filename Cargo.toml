[workspace]
resolver = "2"
members = [
    "yoshi",         # facade
    "yoshi-core",    # no_std foundation
    "yoshi-std",     # core engine
    "yoshi-derive",  # procedural macros
    "yoshi-deluxe",  # deluxe features
    "yoshi-benches", # comprehensive benchmarks
]
# Exclude benchmarks from packaging operations
exclude = [
    "yoshi-benches", # benchmarks don't need to be published
]

# -------- Shared build profiles --------
[profile.dev] # fast ≈ release-like debug builds
opt-level = 1           # some inlining & vectorisation
codegen-units = 4       # keep incremental relinking quick
debug = true
debug-assertions = true
overflow-checks = true
incremental = true      # lightning-fast rebuilds
panic = "unwind"

[profile.dev.package."yoshi-std"] # micro-optimise hot crate
opt-level = 2

# Enterprise-grade release profile for maximum performance
[profile.release]
opt-level = 3           # Maximum optimization level for performance-critical error handling
lto = "fat"             # Full LTO for maximum cross-crate optimization
codegen-units = 1       # Maximize cross-crate inlining and optimization
panic = "abort"         # Smaller binaries, faster unwinding
debug = false           # No debug info in release
strip = "symbols"       # Strip symbols for smaller binaries
rpath = false           # Disable rpath for security

# Performance-optimized release with debug info (for profiling)
[profile.release-with-debug]
inherits = "release"
debug = 1               # Line tables only for profiling
strip = "none"          # Keep symbols for profiling tools

# Size-optimized release profile
[profile.release-small]
inherits = "release"
opt-level = "z"         # Optimize for size
lto = "thin"            # Balanced LTO for size/compile time

# Benchmark-specific optimizations
[profile.bench]
opt-level = 3           # Maximum performance for benchmarks
lto = "fat"             # Full LTO for accurate benchmarking
codegen-units = 1       # Single codegen unit for consistency
debug = false
panic = "abort"
overflow-checks = false # Disable for pure performance measurement

# Test profile with some optimizations for faster test execution
[profile.test]
opt-level = 1           # Some optimization for faster test execution
debug = true            # Keep debug info for test failures
overflow-checks = true  # Keep safety checks in tests
incremental = true      # Faster test compilation

# Workspace dependencies for benchmarking
[workspace.dependencies]
criterion = { version = "0.6.0", features = [
    "html_reports",
    "csv_output",
    "cargo_bench_support",
] }
rayon = "1.10.0"
serde_json = "1.0.140"
serde = { version = "1.0.219", features = ["derive"] }
tokio = { version = "1.45.1", features = ["full"] }
