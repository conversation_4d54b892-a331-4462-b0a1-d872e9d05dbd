[package]
name = "yoshi"
version = "0.1.6"
edition = "2021"
rust-version = "1.87.0"                                               # MSRV
authors = ["<PERSON> <<PERSON><PERSON><PERSON>@proton.me>"]
repository = "https://github.com/arcmoonstudios/yoshi"
license = "MIT OR Apache-2.0"
description = "Entry for the Yoshi error framework."
keywords = ["error", "error-handling", "result", "yoshi", "std-only"]
categories = ["development-tools", "rust-patterns", "api-bindings"]

[dependencies]
yoshi-std = { version = "0.1.6", path = "../yoshi-std", default-features = false }
yoshi-derive = { version = "0.1.6", path = "../yoshi-derive", optional = true }
yoshi-deluxe = { version = "0.1.6", path = "../yoshi-deluxe", optional = true }
serde = { version = "1.0.219", optional = true, features = ["derive"] }
tokio = { version = "1.42.0", optional = true, features = ["full"] }
serde_json = { version = "1.0.140", optional = true }
once_cell = { version = "1.21.3", optional = true }
tracing = { version = "0.1.41", optional = true }
miette = { version = "7.6.0", optional = true }

[features]
default = ["std", "rust-1-87"]
std = ["yoshi-std/std"]
derive = ["yoshi-derive", "yoshi-std/derive"]
serde = ["dep:serde", "dep:serde_json", "yoshi-std/serde"]
tracing = ["dep:tracing", "yoshi-std/tracing"]
# Enhanced Rust 1.87 features
rust-1-87 = ["dep:once_cell", "simd-optimized", "precise-capturing"]
async = ["dep:tokio", "rust-1-87"]
simd-optimized = ["yoshi-std/simd-optimized"]
precise-capturing = []
# convenience
full = ["std", "derive", "serde", "tracing", "rust-1-87", "async"]
# Enhanced feature flags
pipe = ["rust-1-87"]
cross-process = ["pipe", "serde"]
performance-monitoring = ["rust-1-87"]
cli = ["std"]
# LSP integration support (pass-through to yoshi-deluxe)
lsp-integration = ["yoshi-deluxe/lsp-integration"]

# docs.rs specific configuration for robust builds
[package.metadata.docs.rs]
rustc-args = ["--cap-lints=warn"]
# CRITICAL FIX: Use stable feature set for nightly compatibility
features = ["std", "derive", "serde", "tracing", "rust-1-87"]
no-default-features = false
rustdoc-args = ["--cfg", "docsrs"]
targets = ["x86_64-unknown-linux-gnu"]
