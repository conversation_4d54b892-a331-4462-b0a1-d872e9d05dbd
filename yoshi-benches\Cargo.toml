[package]
name = "yoshi-benches"
version = "0.1.6"
edition = "2021"
publish = false

# Benchmark configurations
[[bench]]
name = "cross_crate_integration"
harness = false

[[bench]]
name = "error_contest"
harness = false

[[bench]]
name = "error_context"
harness = false

[[bench]]
name = "error_conversion"
harness = false

[[bench]]
name = "error_creation"
harness = false

[[bench]]
name = "error_formatting"
harness = false

[dependencies]
# Local workspace dependencies - Yoshi framework components
yoshi = { path = "../yoshi", version = "0.1.6" }
yoshi-std = { path = "../yoshi-std", version = "0.1.6" }
yoshi-derive = { path = "../yoshi-derive", version = "0.1.6", default-features = true, features = [
    "std",
] }

# Benchmarking infrastructure
criterion = { version = "0.6", features = [
    "html_reports",
    "csv_output",
    "cargo_bench_support",
] }

# Supporting dependencies for benchmark scenarios
tokio = { version = "1.45.1", features = ["full"] }
serde = { version = "1.0.219", features = ["derive"] }
serde_json = "1.0.140"
rayon = "1.10.0"

# Comparison dependencies (ONLY for side-by-side performance comparison)
anyhow = { version = "1.0.98", optional = true }
eyre = { version = "0.6.12", optional = true }
snafu = { version = "0.8.6", optional = true }
thiserror = { version = "2.0.12", optional = true }

[features]
default = ["comparison"]
comparison = ["dep:thiserror", "dep:anyhow", "dep:eyre", "dep:snafu"]
# Standard library support (pass-through for compatibility)
std = []

[dev-dependencies]
criterion = { version = "0.6.0", features = [
    "html_reports",
    "csv_output",
    "cargo_bench_support",
] }
