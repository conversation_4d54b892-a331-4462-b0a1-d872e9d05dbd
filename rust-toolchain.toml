# rust-toolchain.toml ── shared by all crates in the workspace
[toolchain]
channel    = "1.87.0"       # MSRV – keep in sync with yoshi-std’s manifest
profile    = "minimal"      # tiny install footprint, still gets rustfmt + clippy
components = [
  "clippy",
  "rustfmt",
  "rust-src",               # needed for no_std + building proc-macros on embedded targets
]

targets = [
  "x86_64-unknown-linux-gnu",
  "x86_64-pc-windows-msvc",
  "aarch64-apple-darwin",
  "thumbv7em-none-eabihf",  # the no_std smoke-test target used in CI
]

[build]
rustflags = [
  "-C", "link-arg=-fuse-ld=lld"   # 2-3× faster link on LLVM-based hosts
]

[env]
# Respect workspace-level optimisation choices but keep compile times snappy
CARGO_INCREMENTAL = { value = "1", force = true }
