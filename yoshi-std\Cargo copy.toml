[package]
name = "yoshi-std"
version = "0.1.6"
edition = "2021"
rust-version = "1.75"
description = "Standard library integration and full implementation for the Yoshi error handling ecosystem"
authors = ["<PERSON>yn <<EMAIL>>"]
license = "MIT OR Apache-2.0"
repository = "https://github.com/arcmoonstudios/yoshi-std"
documentation = "https://docs.rs/yoshi-std"
homepage = "https://github.com/arcmoonstudios/yoshi-std"
keywords = ["error", "error-handling", "std", "backtrace", "diagnostics"]
categories = ["development-tools::debugging", "rust-patterns"]
readme = "README.md"
exclude = [".github/*", "examples/*", "benches/*", "tests/*"]

[features]
default = ["serde"]

# Serialization support (re-exported from yoshi-core)
serde = ["yoshi-core/serde"]

# Enhanced debugging features
enhanced-backtrace = []

# Development and testing features
testing = []

[dependencies]
# Core yoshi functionality
yoshi-core = { version = "0.1.0", path = "../yoshi-core", features = ["std"] }

[dev-dependencies]
# Testing utilities
serde_json = "1.0"
tokio = { version = "1.0", features = ["full"] }

# For testing std::io::Error integration
tempfile = "3.8"

[package.metadata.docs.rs]
all-features = true
rustdoc-args = ["--cfg", "docsrs"]

[[example]]
name = "basic_usage"

[[example]]
name = "io_integration"

[[example]]
name = "advanced_context"

[[example]]
name = "backtrace_demo"
required-features = ["enhanced-backtrace"]

[[example]]
name = "foreign_errors"

[[example]]
name = "serde_support"
required-features = ["serde"]
