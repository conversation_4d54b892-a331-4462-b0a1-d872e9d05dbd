# Open Source Licensing FAQ

## Frequently Asked Questions

### What license does Yoshi use?

Yoshi is dual-licensed under MIT OR Apache-2.0, giving you the choice of either license for maximum flexibility.

### Can I use Yoshi in commercial projects?

Yes! Both MIT and Apache-2.0 licenses allow unrestricted commercial use, including in proprietary products.

### Can I modify and redistribute Yoshi?

Absolutely! You can modify, redistribute, and even sell products that include Yoshi under either license.

### Do I need to include license notices?

Yes, you must include the appropriate license notice (MIT or Apache-2.0) when redistributing Yoshi or products containing it.

### How do I contribute to Yoshi?

Contributions are dual-licensed under MIT OR Apache-2.0. Simply submit a pull request - no CLA required!

### Why dual licensing?

MIT and Apache-2.0 are the most popular choices in the Rust ecosystem, giving users maximum compatibility and flexibility.

### Can I use a different license for my modifications?

For derivatives, you must maintain the MIT OR Apache-2.0 licensing. For separate works that use Yoshi as a dependency, you can choose any license.
