// This file is @generated by syn-internal-codegen.
// It is not intended for manual editing.

#![allow(unknown_lints, non_local_definitions)]
use std::fmt::{self, Debug};
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::Abi {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        let mut formatter = formatter.debug_struct("Abi");
        formatter.field("extern_token", &self.extern_token);
        formatter.field("name", &self.name);
        formatter.finish()
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::AngleBracketedGenericArguments {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "AngleBracketedGenericArguments")
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
impl crate::AngleBracketedGenericArguments {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("colon2_token", &self.colon2_token);
        formatter.field("lt_token", &self.lt_token);
        formatter.field("args", &self.args);
        formatter.field("gt_token", &self.gt_token);
        formatter.finish()
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::Arm {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        let mut formatter = formatter.debug_struct("Arm");
        formatter.field("attrs", &self.attrs);
        formatter.field("pat", &self.pat);
        formatter.field("guard", &self.guard);
        formatter.field("fat_arrow_token", &self.fat_arrow_token);
        formatter.field("body", &self.body);
        formatter.field("comma", &self.comma);
        formatter.finish()
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::AssocConst {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        let mut formatter = formatter.debug_struct("AssocConst");
        formatter.field("ident", &self.ident);
        formatter.field("generics", &self.generics);
        formatter.field("eq_token", &self.eq_token);
        formatter.field("value", &self.value);
        formatter.finish()
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::AssocType {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        let mut formatter = formatter.debug_struct("AssocType");
        formatter.field("ident", &self.ident);
        formatter.field("generics", &self.generics);
        formatter.field("eq_token", &self.eq_token);
        formatter.field("ty", &self.ty);
        formatter.finish()
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::AttrStyle {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        formatter.write_str("AttrStyle::")?;
        match self {
            crate::AttrStyle::Outer => formatter.write_str("Outer"),
            crate::AttrStyle::Inner(v0) => {
                let mut formatter = formatter.debug_tuple("Inner");
                formatter.field(v0);
                formatter.finish()
            }
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::Attribute {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        let mut formatter = formatter.debug_struct("Attribute");
        formatter.field("pound_token", &self.pound_token);
        formatter.field("style", &self.style);
        formatter.field("bracket_token", &self.bracket_token);
        formatter.field("meta", &self.meta);
        formatter.finish()
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::BareFnArg {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        let mut formatter = formatter.debug_struct("BareFnArg");
        formatter.field("attrs", &self.attrs);
        formatter.field("name", &self.name);
        formatter.field("ty", &self.ty);
        formatter.finish()
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::BareVariadic {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        let mut formatter = formatter.debug_struct("BareVariadic");
        formatter.field("attrs", &self.attrs);
        formatter.field("name", &self.name);
        formatter.field("dots", &self.dots);
        formatter.field("comma", &self.comma);
        formatter.finish()
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::BinOp {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        formatter.write_str("BinOp::")?;
        match self {
            crate::BinOp::Add(v0) => {
                let mut formatter = formatter.debug_tuple("Add");
                formatter.field(v0);
                formatter.finish()
            }
            crate::BinOp::Sub(v0) => {
                let mut formatter = formatter.debug_tuple("Sub");
                formatter.field(v0);
                formatter.finish()
            }
            crate::BinOp::Mul(v0) => {
                let mut formatter = formatter.debug_tuple("Mul");
                formatter.field(v0);
                formatter.finish()
            }
            crate::BinOp::Div(v0) => {
                let mut formatter = formatter.debug_tuple("Div");
                formatter.field(v0);
                formatter.finish()
            }
            crate::BinOp::Rem(v0) => {
                let mut formatter = formatter.debug_tuple("Rem");
                formatter.field(v0);
                formatter.finish()
            }
            crate::BinOp::And(v0) => {
                let mut formatter = formatter.debug_tuple("And");
                formatter.field(v0);
                formatter.finish()
            }
            crate::BinOp::Or(v0) => {
                let mut formatter = formatter.debug_tuple("Or");
                formatter.field(v0);
                formatter.finish()
            }
            crate::BinOp::BitXor(v0) => {
                let mut formatter = formatter.debug_tuple("BitXor");
                formatter.field(v0);
                formatter.finish()
            }
            crate::BinOp::BitAnd(v0) => {
                let mut formatter = formatter.debug_tuple("BitAnd");
                formatter.field(v0);
                formatter.finish()
            }
            crate::BinOp::BitOr(v0) => {
                let mut formatter = formatter.debug_tuple("BitOr");
                formatter.field(v0);
                formatter.finish()
            }
            crate::BinOp::Shl(v0) => {
                let mut formatter = formatter.debug_tuple("Shl");
                formatter.field(v0);
                formatter.finish()
            }
            crate::BinOp::Shr(v0) => {
                let mut formatter = formatter.debug_tuple("Shr");
                formatter.field(v0);
                formatter.finish()
            }
            crate::BinOp::Eq(v0) => {
                let mut formatter = formatter.debug_tuple("Eq");
                formatter.field(v0);
                formatter.finish()
            }
            crate::BinOp::Lt(v0) => {
                let mut formatter = formatter.debug_tuple("Lt");
                formatter.field(v0);
                formatter.finish()
            }
            crate::BinOp::Le(v0) => {
                let mut formatter = formatter.debug_tuple("Le");
                formatter.field(v0);
                formatter.finish()
            }
            crate::BinOp::Ne(v0) => {
                let mut formatter = formatter.debug_tuple("Ne");
                formatter.field(v0);
                formatter.finish()
            }
            crate::BinOp::Ge(v0) => {
                let mut formatter = formatter.debug_tuple("Ge");
                formatter.field(v0);
                formatter.finish()
            }
            crate::BinOp::Gt(v0) => {
                let mut formatter = formatter.debug_tuple("Gt");
                formatter.field(v0);
                formatter.finish()
            }
            crate::BinOp::AddAssign(v0) => {
                let mut formatter = formatter.debug_tuple("AddAssign");
                formatter.field(v0);
                formatter.finish()
            }
            crate::BinOp::SubAssign(v0) => {
                let mut formatter = formatter.debug_tuple("SubAssign");
                formatter.field(v0);
                formatter.finish()
            }
            crate::BinOp::MulAssign(v0) => {
                let mut formatter = formatter.debug_tuple("MulAssign");
                formatter.field(v0);
                formatter.finish()
            }
            crate::BinOp::DivAssign(v0) => {
                let mut formatter = formatter.debug_tuple("DivAssign");
                formatter.field(v0);
                formatter.finish()
            }
            crate::BinOp::RemAssign(v0) => {
                let mut formatter = formatter.debug_tuple("RemAssign");
                formatter.field(v0);
                formatter.finish()
            }
            crate::BinOp::BitXorAssign(v0) => {
                let mut formatter = formatter.debug_tuple("BitXorAssign");
                formatter.field(v0);
                formatter.finish()
            }
            crate::BinOp::BitAndAssign(v0) => {
                let mut formatter = formatter.debug_tuple("BitAndAssign");
                formatter.field(v0);
                formatter.finish()
            }
            crate::BinOp::BitOrAssign(v0) => {
                let mut formatter = formatter.debug_tuple("BitOrAssign");
                formatter.field(v0);
                formatter.finish()
            }
            crate::BinOp::ShlAssign(v0) => {
                let mut formatter = formatter.debug_tuple("ShlAssign");
                formatter.field(v0);
                formatter.finish()
            }
            crate::BinOp::ShrAssign(v0) => {
                let mut formatter = formatter.debug_tuple("ShrAssign");
                formatter.field(v0);
                formatter.finish()
            }
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::Block {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        let mut formatter = formatter.debug_struct("Block");
        formatter.field("brace_token", &self.brace_token);
        formatter.field("stmts", &self.stmts);
        formatter.finish()
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::BoundLifetimes {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        let mut formatter = formatter.debug_struct("BoundLifetimes");
        formatter.field("for_token", &self.for_token);
        formatter.field("lt_token", &self.lt_token);
        formatter.field("lifetimes", &self.lifetimes);
        formatter.field("gt_token", &self.gt_token);
        formatter.finish()
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::CapturedParam {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        formatter.write_str("CapturedParam::")?;
        match self {
            crate::CapturedParam::Lifetime(v0) => {
                let mut formatter = formatter.debug_tuple("Lifetime");
                formatter.field(v0);
                formatter.finish()
            }
            crate::CapturedParam::Ident(v0) => {
                let mut formatter = formatter.debug_tuple("Ident");
                formatter.field(v0);
                formatter.finish()
            }
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::ConstParam {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        let mut formatter = formatter.debug_struct("ConstParam");
        formatter.field("attrs", &self.attrs);
        formatter.field("const_token", &self.const_token);
        formatter.field("ident", &self.ident);
        formatter.field("colon_token", &self.colon_token);
        formatter.field("ty", &self.ty);
        formatter.field("eq_token", &self.eq_token);
        formatter.field("default", &self.default);
        formatter.finish()
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::Constraint {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        let mut formatter = formatter.debug_struct("Constraint");
        formatter.field("ident", &self.ident);
        formatter.field("generics", &self.generics);
        formatter.field("colon_token", &self.colon_token);
        formatter.field("bounds", &self.bounds);
        formatter.finish()
    }
}
#[cfg(feature = "derive")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::Data {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        formatter.write_str("Data::")?;
        match self {
            crate::Data::Struct(v0) => v0.debug(formatter, "Struct"),
            crate::Data::Enum(v0) => v0.debug(formatter, "Enum"),
            crate::Data::Union(v0) => v0.debug(formatter, "Union"),
        }
    }
}
#[cfg(feature = "derive")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::DataEnum {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "DataEnum")
    }
}
#[cfg(feature = "derive")]
impl crate::DataEnum {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("enum_token", &self.enum_token);
        formatter.field("brace_token", &self.brace_token);
        formatter.field("variants", &self.variants);
        formatter.finish()
    }
}
#[cfg(feature = "derive")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::DataStruct {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "DataStruct")
    }
}
#[cfg(feature = "derive")]
impl crate::DataStruct {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("struct_token", &self.struct_token);
        formatter.field("fields", &self.fields);
        formatter.field("semi_token", &self.semi_token);
        formatter.finish()
    }
}
#[cfg(feature = "derive")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::DataUnion {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "DataUnion")
    }
}
#[cfg(feature = "derive")]
impl crate::DataUnion {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("union_token", &self.union_token);
        formatter.field("fields", &self.fields);
        formatter.finish()
    }
}
#[cfg(feature = "derive")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::DeriveInput {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        let mut formatter = formatter.debug_struct("DeriveInput");
        formatter.field("attrs", &self.attrs);
        formatter.field("vis", &self.vis);
        formatter.field("ident", &self.ident);
        formatter.field("generics", &self.generics);
        formatter.field("data", &self.data);
        formatter.finish()
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::Expr {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        formatter.write_str("Expr::")?;
        match self {
            #[cfg(feature = "full")]
            crate::Expr::Array(v0) => v0.debug(formatter, "Array"),
            #[cfg(feature = "full")]
            crate::Expr::Assign(v0) => v0.debug(formatter, "Assign"),
            #[cfg(feature = "full")]
            crate::Expr::Async(v0) => v0.debug(formatter, "Async"),
            #[cfg(feature = "full")]
            crate::Expr::Await(v0) => v0.debug(formatter, "Await"),
            crate::Expr::Binary(v0) => v0.debug(formatter, "Binary"),
            #[cfg(feature = "full")]
            crate::Expr::Block(v0) => v0.debug(formatter, "Block"),
            #[cfg(feature = "full")]
            crate::Expr::Break(v0) => v0.debug(formatter, "Break"),
            crate::Expr::Call(v0) => v0.debug(formatter, "Call"),
            crate::Expr::Cast(v0) => v0.debug(formatter, "Cast"),
            #[cfg(feature = "full")]
            crate::Expr::Closure(v0) => v0.debug(formatter, "Closure"),
            #[cfg(feature = "full")]
            crate::Expr::Const(v0) => v0.debug(formatter, "Const"),
            #[cfg(feature = "full")]
            crate::Expr::Continue(v0) => v0.debug(formatter, "Continue"),
            crate::Expr::Field(v0) => v0.debug(formatter, "Field"),
            #[cfg(feature = "full")]
            crate::Expr::ForLoop(v0) => v0.debug(formatter, "ForLoop"),
            crate::Expr::Group(v0) => v0.debug(formatter, "Group"),
            #[cfg(feature = "full")]
            crate::Expr::If(v0) => v0.debug(formatter, "If"),
            crate::Expr::Index(v0) => v0.debug(formatter, "Index"),
            #[cfg(feature = "full")]
            crate::Expr::Infer(v0) => v0.debug(formatter, "Infer"),
            #[cfg(feature = "full")]
            crate::Expr::Let(v0) => v0.debug(formatter, "Let"),
            crate::Expr::Lit(v0) => v0.debug(formatter, "Lit"),
            #[cfg(feature = "full")]
            crate::Expr::Loop(v0) => v0.debug(formatter, "Loop"),
            crate::Expr::Macro(v0) => v0.debug(formatter, "Macro"),
            #[cfg(feature = "full")]
            crate::Expr::Match(v0) => v0.debug(formatter, "Match"),
            crate::Expr::MethodCall(v0) => v0.debug(formatter, "MethodCall"),
            crate::Expr::Paren(v0) => v0.debug(formatter, "Paren"),
            crate::Expr::Path(v0) => v0.debug(formatter, "Path"),
            #[cfg(feature = "full")]
            crate::Expr::Range(v0) => v0.debug(formatter, "Range"),
            #[cfg(feature = "full")]
            crate::Expr::RawAddr(v0) => v0.debug(formatter, "RawAddr"),
            crate::Expr::Reference(v0) => v0.debug(formatter, "Reference"),
            #[cfg(feature = "full")]
            crate::Expr::Repeat(v0) => v0.debug(formatter, "Repeat"),
            #[cfg(feature = "full")]
            crate::Expr::Return(v0) => v0.debug(formatter, "Return"),
            crate::Expr::Struct(v0) => v0.debug(formatter, "Struct"),
            #[cfg(feature = "full")]
            crate::Expr::Try(v0) => v0.debug(formatter, "Try"),
            #[cfg(feature = "full")]
            crate::Expr::TryBlock(v0) => v0.debug(formatter, "TryBlock"),
            crate::Expr::Tuple(v0) => v0.debug(formatter, "Tuple"),
            crate::Expr::Unary(v0) => v0.debug(formatter, "Unary"),
            #[cfg(feature = "full")]
            crate::Expr::Unsafe(v0) => v0.debug(formatter, "Unsafe"),
            crate::Expr::Verbatim(v0) => {
                let mut formatter = formatter.debug_tuple("Verbatim");
                formatter.field(v0);
                formatter.finish()
            }
            #[cfg(feature = "full")]
            crate::Expr::While(v0) => v0.debug(formatter, "While"),
            #[cfg(feature = "full")]
            crate::Expr::Yield(v0) => v0.debug(formatter, "Yield"),
            #[cfg(not(feature = "full"))]
            _ => unreachable!(),
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::ExprArray {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "ExprArray")
    }
}
#[cfg(feature = "full")]
impl crate::ExprArray {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("attrs", &self.attrs);
        formatter.field("bracket_token", &self.bracket_token);
        formatter.field("elems", &self.elems);
        formatter.finish()
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::ExprAssign {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "ExprAssign")
    }
}
#[cfg(feature = "full")]
impl crate::ExprAssign {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("attrs", &self.attrs);
        formatter.field("left", &self.left);
        formatter.field("eq_token", &self.eq_token);
        formatter.field("right", &self.right);
        formatter.finish()
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::ExprAsync {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "ExprAsync")
    }
}
#[cfg(feature = "full")]
impl crate::ExprAsync {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("attrs", &self.attrs);
        formatter.field("async_token", &self.async_token);
        formatter.field("capture", &self.capture);
        formatter.field("block", &self.block);
        formatter.finish()
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::ExprAwait {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "ExprAwait")
    }
}
#[cfg(feature = "full")]
impl crate::ExprAwait {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("attrs", &self.attrs);
        formatter.field("base", &self.base);
        formatter.field("dot_token", &self.dot_token);
        formatter.field("await_token", &self.await_token);
        formatter.finish()
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::ExprBinary {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "ExprBinary")
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
impl crate::ExprBinary {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("attrs", &self.attrs);
        formatter.field("left", &self.left);
        formatter.field("op", &self.op);
        formatter.field("right", &self.right);
        formatter.finish()
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::ExprBlock {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "ExprBlock")
    }
}
#[cfg(feature = "full")]
impl crate::ExprBlock {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("attrs", &self.attrs);
        formatter.field("label", &self.label);
        formatter.field("block", &self.block);
        formatter.finish()
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::ExprBreak {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "ExprBreak")
    }
}
#[cfg(feature = "full")]
impl crate::ExprBreak {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("attrs", &self.attrs);
        formatter.field("break_token", &self.break_token);
        formatter.field("label", &self.label);
        formatter.field("expr", &self.expr);
        formatter.finish()
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::ExprCall {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "ExprCall")
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
impl crate::ExprCall {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("attrs", &self.attrs);
        formatter.field("func", &self.func);
        formatter.field("paren_token", &self.paren_token);
        formatter.field("args", &self.args);
        formatter.finish()
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::ExprCast {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "ExprCast")
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
impl crate::ExprCast {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("attrs", &self.attrs);
        formatter.field("expr", &self.expr);
        formatter.field("as_token", &self.as_token);
        formatter.field("ty", &self.ty);
        formatter.finish()
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::ExprClosure {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "ExprClosure")
    }
}
#[cfg(feature = "full")]
impl crate::ExprClosure {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("attrs", &self.attrs);
        formatter.field("lifetimes", &self.lifetimes);
        formatter.field("constness", &self.constness);
        formatter.field("movability", &self.movability);
        formatter.field("asyncness", &self.asyncness);
        formatter.field("capture", &self.capture);
        formatter.field("or1_token", &self.or1_token);
        formatter.field("inputs", &self.inputs);
        formatter.field("or2_token", &self.or2_token);
        formatter.field("output", &self.output);
        formatter.field("body", &self.body);
        formatter.finish()
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::ExprConst {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "ExprConst")
    }
}
#[cfg(feature = "full")]
impl crate::ExprConst {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("attrs", &self.attrs);
        formatter.field("const_token", &self.const_token);
        formatter.field("block", &self.block);
        formatter.finish()
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::ExprContinue {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "ExprContinue")
    }
}
#[cfg(feature = "full")]
impl crate::ExprContinue {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("attrs", &self.attrs);
        formatter.field("continue_token", &self.continue_token);
        formatter.field("label", &self.label);
        formatter.finish()
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::ExprField {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "ExprField")
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
impl crate::ExprField {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("attrs", &self.attrs);
        formatter.field("base", &self.base);
        formatter.field("dot_token", &self.dot_token);
        formatter.field("member", &self.member);
        formatter.finish()
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::ExprForLoop {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "ExprForLoop")
    }
}
#[cfg(feature = "full")]
impl crate::ExprForLoop {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("attrs", &self.attrs);
        formatter.field("label", &self.label);
        formatter.field("for_token", &self.for_token);
        formatter.field("pat", &self.pat);
        formatter.field("in_token", &self.in_token);
        formatter.field("expr", &self.expr);
        formatter.field("body", &self.body);
        formatter.finish()
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::ExprGroup {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "ExprGroup")
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
impl crate::ExprGroup {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("attrs", &self.attrs);
        formatter.field("group_token", &self.group_token);
        formatter.field("expr", &self.expr);
        formatter.finish()
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::ExprIf {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "ExprIf")
    }
}
#[cfg(feature = "full")]
impl crate::ExprIf {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("attrs", &self.attrs);
        formatter.field("if_token", &self.if_token);
        formatter.field("cond", &self.cond);
        formatter.field("then_branch", &self.then_branch);
        formatter.field("else_branch", &self.else_branch);
        formatter.finish()
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::ExprIndex {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "ExprIndex")
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
impl crate::ExprIndex {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("attrs", &self.attrs);
        formatter.field("expr", &self.expr);
        formatter.field("bracket_token", &self.bracket_token);
        formatter.field("index", &self.index);
        formatter.finish()
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::ExprInfer {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "ExprInfer")
    }
}
#[cfg(feature = "full")]
impl crate::ExprInfer {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("attrs", &self.attrs);
        formatter.field("underscore_token", &self.underscore_token);
        formatter.finish()
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::ExprLet {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "ExprLet")
    }
}
#[cfg(feature = "full")]
impl crate::ExprLet {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("attrs", &self.attrs);
        formatter.field("let_token", &self.let_token);
        formatter.field("pat", &self.pat);
        formatter.field("eq_token", &self.eq_token);
        formatter.field("expr", &self.expr);
        formatter.finish()
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::ExprLit {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "ExprLit")
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
impl crate::ExprLit {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("attrs", &self.attrs);
        formatter.field("lit", &self.lit);
        formatter.finish()
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::ExprLoop {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "ExprLoop")
    }
}
#[cfg(feature = "full")]
impl crate::ExprLoop {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("attrs", &self.attrs);
        formatter.field("label", &self.label);
        formatter.field("loop_token", &self.loop_token);
        formatter.field("body", &self.body);
        formatter.finish()
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::ExprMacro {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "ExprMacro")
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
impl crate::ExprMacro {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("attrs", &self.attrs);
        formatter.field("mac", &self.mac);
        formatter.finish()
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::ExprMatch {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "ExprMatch")
    }
}
#[cfg(feature = "full")]
impl crate::ExprMatch {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("attrs", &self.attrs);
        formatter.field("match_token", &self.match_token);
        formatter.field("expr", &self.expr);
        formatter.field("brace_token", &self.brace_token);
        formatter.field("arms", &self.arms);
        formatter.finish()
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::ExprMethodCall {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "ExprMethodCall")
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
impl crate::ExprMethodCall {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("attrs", &self.attrs);
        formatter.field("receiver", &self.receiver);
        formatter.field("dot_token", &self.dot_token);
        formatter.field("method", &self.method);
        formatter.field("turbofish", &self.turbofish);
        formatter.field("paren_token", &self.paren_token);
        formatter.field("args", &self.args);
        formatter.finish()
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::ExprParen {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "ExprParen")
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
impl crate::ExprParen {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("attrs", &self.attrs);
        formatter.field("paren_token", &self.paren_token);
        formatter.field("expr", &self.expr);
        formatter.finish()
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::ExprPath {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "ExprPath")
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
impl crate::ExprPath {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("attrs", &self.attrs);
        formatter.field("qself", &self.qself);
        formatter.field("path", &self.path);
        formatter.finish()
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::ExprRange {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "ExprRange")
    }
}
#[cfg(feature = "full")]
impl crate::ExprRange {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("attrs", &self.attrs);
        formatter.field("start", &self.start);
        formatter.field("limits", &self.limits);
        formatter.field("end", &self.end);
        formatter.finish()
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::ExprRawAddr {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "ExprRawAddr")
    }
}
#[cfg(feature = "full")]
impl crate::ExprRawAddr {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("attrs", &self.attrs);
        formatter.field("and_token", &self.and_token);
        formatter.field("raw", &self.raw);
        formatter.field("mutability", &self.mutability);
        formatter.field("expr", &self.expr);
        formatter.finish()
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::ExprReference {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "ExprReference")
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
impl crate::ExprReference {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("attrs", &self.attrs);
        formatter.field("and_token", &self.and_token);
        formatter.field("mutability", &self.mutability);
        formatter.field("expr", &self.expr);
        formatter.finish()
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::ExprRepeat {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "ExprRepeat")
    }
}
#[cfg(feature = "full")]
impl crate::ExprRepeat {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("attrs", &self.attrs);
        formatter.field("bracket_token", &self.bracket_token);
        formatter.field("expr", &self.expr);
        formatter.field("semi_token", &self.semi_token);
        formatter.field("len", &self.len);
        formatter.finish()
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::ExprReturn {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "ExprReturn")
    }
}
#[cfg(feature = "full")]
impl crate::ExprReturn {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("attrs", &self.attrs);
        formatter.field("return_token", &self.return_token);
        formatter.field("expr", &self.expr);
        formatter.finish()
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::ExprStruct {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "ExprStruct")
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
impl crate::ExprStruct {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("attrs", &self.attrs);
        formatter.field("qself", &self.qself);
        formatter.field("path", &self.path);
        formatter.field("brace_token", &self.brace_token);
        formatter.field("fields", &self.fields);
        formatter.field("dot2_token", &self.dot2_token);
        formatter.field("rest", &self.rest);
        formatter.finish()
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::ExprTry {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "ExprTry")
    }
}
#[cfg(feature = "full")]
impl crate::ExprTry {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("attrs", &self.attrs);
        formatter.field("expr", &self.expr);
        formatter.field("question_token", &self.question_token);
        formatter.finish()
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::ExprTryBlock {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "ExprTryBlock")
    }
}
#[cfg(feature = "full")]
impl crate::ExprTryBlock {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("attrs", &self.attrs);
        formatter.field("try_token", &self.try_token);
        formatter.field("block", &self.block);
        formatter.finish()
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::ExprTuple {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "ExprTuple")
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
impl crate::ExprTuple {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("attrs", &self.attrs);
        formatter.field("paren_token", &self.paren_token);
        formatter.field("elems", &self.elems);
        formatter.finish()
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::ExprUnary {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "ExprUnary")
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
impl crate::ExprUnary {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("attrs", &self.attrs);
        formatter.field("op", &self.op);
        formatter.field("expr", &self.expr);
        formatter.finish()
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::ExprUnsafe {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "ExprUnsafe")
    }
}
#[cfg(feature = "full")]
impl crate::ExprUnsafe {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("attrs", &self.attrs);
        formatter.field("unsafe_token", &self.unsafe_token);
        formatter.field("block", &self.block);
        formatter.finish()
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::ExprWhile {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "ExprWhile")
    }
}
#[cfg(feature = "full")]
impl crate::ExprWhile {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("attrs", &self.attrs);
        formatter.field("label", &self.label);
        formatter.field("while_token", &self.while_token);
        formatter.field("cond", &self.cond);
        formatter.field("body", &self.body);
        formatter.finish()
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::ExprYield {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "ExprYield")
    }
}
#[cfg(feature = "full")]
impl crate::ExprYield {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("attrs", &self.attrs);
        formatter.field("yield_token", &self.yield_token);
        formatter.field("expr", &self.expr);
        formatter.finish()
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::Field {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        let mut formatter = formatter.debug_struct("Field");
        formatter.field("attrs", &self.attrs);
        formatter.field("vis", &self.vis);
        formatter.field("mutability", &self.mutability);
        formatter.field("ident", &self.ident);
        formatter.field("colon_token", &self.colon_token);
        formatter.field("ty", &self.ty);
        formatter.finish()
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::FieldMutability {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        formatter.write_str("FieldMutability::")?;
        match self {
            crate::FieldMutability::None => formatter.write_str("None"),
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::FieldPat {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        let mut formatter = formatter.debug_struct("FieldPat");
        formatter.field("attrs", &self.attrs);
        formatter.field("member", &self.member);
        formatter.field("colon_token", &self.colon_token);
        formatter.field("pat", &self.pat);
        formatter.finish()
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::FieldValue {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        let mut formatter = formatter.debug_struct("FieldValue");
        formatter.field("attrs", &self.attrs);
        formatter.field("member", &self.member);
        formatter.field("colon_token", &self.colon_token);
        formatter.field("expr", &self.expr);
        formatter.finish()
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::Fields {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        formatter.write_str("Fields::")?;
        match self {
            crate::Fields::Named(v0) => v0.debug(formatter, "Named"),
            crate::Fields::Unnamed(v0) => v0.debug(formatter, "Unnamed"),
            crate::Fields::Unit => formatter.write_str("Unit"),
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::FieldsNamed {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "FieldsNamed")
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
impl crate::FieldsNamed {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("brace_token", &self.brace_token);
        formatter.field("named", &self.named);
        formatter.finish()
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::FieldsUnnamed {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "FieldsUnnamed")
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
impl crate::FieldsUnnamed {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("paren_token", &self.paren_token);
        formatter.field("unnamed", &self.unnamed);
        formatter.finish()
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::File {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        let mut formatter = formatter.debug_struct("File");
        formatter.field("shebang", &self.shebang);
        formatter.field("attrs", &self.attrs);
        formatter.field("items", &self.items);
        formatter.finish()
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::FnArg {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        formatter.write_str("FnArg::")?;
        match self {
            crate::FnArg::Receiver(v0) => {
                let mut formatter = formatter.debug_tuple("Receiver");
                formatter.field(v0);
                formatter.finish()
            }
            crate::FnArg::Typed(v0) => {
                let mut formatter = formatter.debug_tuple("Typed");
                formatter.field(v0);
                formatter.finish()
            }
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::ForeignItem {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        formatter.write_str("ForeignItem::")?;
        match self {
            crate::ForeignItem::Fn(v0) => v0.debug(formatter, "Fn"),
            crate::ForeignItem::Static(v0) => v0.debug(formatter, "Static"),
            crate::ForeignItem::Type(v0) => v0.debug(formatter, "Type"),
            crate::ForeignItem::Macro(v0) => v0.debug(formatter, "Macro"),
            crate::ForeignItem::Verbatim(v0) => {
                let mut formatter = formatter.debug_tuple("Verbatim");
                formatter.field(v0);
                formatter.finish()
            }
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::ForeignItemFn {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "ForeignItemFn")
    }
}
#[cfg(feature = "full")]
impl crate::ForeignItemFn {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("attrs", &self.attrs);
        formatter.field("vis", &self.vis);
        formatter.field("sig", &self.sig);
        formatter.field("semi_token", &self.semi_token);
        formatter.finish()
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::ForeignItemMacro {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "ForeignItemMacro")
    }
}
#[cfg(feature = "full")]
impl crate::ForeignItemMacro {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("attrs", &self.attrs);
        formatter.field("mac", &self.mac);
        formatter.field("semi_token", &self.semi_token);
        formatter.finish()
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::ForeignItemStatic {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "ForeignItemStatic")
    }
}
#[cfg(feature = "full")]
impl crate::ForeignItemStatic {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("attrs", &self.attrs);
        formatter.field("vis", &self.vis);
        formatter.field("static_token", &self.static_token);
        formatter.field("mutability", &self.mutability);
        formatter.field("ident", &self.ident);
        formatter.field("colon_token", &self.colon_token);
        formatter.field("ty", &self.ty);
        formatter.field("semi_token", &self.semi_token);
        formatter.finish()
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::ForeignItemType {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "ForeignItemType")
    }
}
#[cfg(feature = "full")]
impl crate::ForeignItemType {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("attrs", &self.attrs);
        formatter.field("vis", &self.vis);
        formatter.field("type_token", &self.type_token);
        formatter.field("ident", &self.ident);
        formatter.field("generics", &self.generics);
        formatter.field("semi_token", &self.semi_token);
        formatter.finish()
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::GenericArgument {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        formatter.write_str("GenericArgument::")?;
        match self {
            crate::GenericArgument::Lifetime(v0) => {
                let mut formatter = formatter.debug_tuple("Lifetime");
                formatter.field(v0);
                formatter.finish()
            }
            crate::GenericArgument::Type(v0) => {
                let mut formatter = formatter.debug_tuple("Type");
                formatter.field(v0);
                formatter.finish()
            }
            crate::GenericArgument::Const(v0) => {
                let mut formatter = formatter.debug_tuple("Const");
                formatter.field(v0);
                formatter.finish()
            }
            crate::GenericArgument::AssocType(v0) => {
                let mut formatter = formatter.debug_tuple("AssocType");
                formatter.field(v0);
                formatter.finish()
            }
            crate::GenericArgument::AssocConst(v0) => {
                let mut formatter = formatter.debug_tuple("AssocConst");
                formatter.field(v0);
                formatter.finish()
            }
            crate::GenericArgument::Constraint(v0) => {
                let mut formatter = formatter.debug_tuple("Constraint");
                formatter.field(v0);
                formatter.finish()
            }
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::GenericParam {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        formatter.write_str("GenericParam::")?;
        match self {
            crate::GenericParam::Lifetime(v0) => {
                let mut formatter = formatter.debug_tuple("Lifetime");
                formatter.field(v0);
                formatter.finish()
            }
            crate::GenericParam::Type(v0) => {
                let mut formatter = formatter.debug_tuple("Type");
                formatter.field(v0);
                formatter.finish()
            }
            crate::GenericParam::Const(v0) => {
                let mut formatter = formatter.debug_tuple("Const");
                formatter.field(v0);
                formatter.finish()
            }
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::Generics {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        let mut formatter = formatter.debug_struct("Generics");
        formatter.field("lt_token", &self.lt_token);
        formatter.field("params", &self.params);
        formatter.field("gt_token", &self.gt_token);
        formatter.field("where_clause", &self.where_clause);
        formatter.finish()
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::ImplItem {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        formatter.write_str("ImplItem::")?;
        match self {
            crate::ImplItem::Const(v0) => v0.debug(formatter, "Const"),
            crate::ImplItem::Fn(v0) => v0.debug(formatter, "Fn"),
            crate::ImplItem::Type(v0) => v0.debug(formatter, "Type"),
            crate::ImplItem::Macro(v0) => v0.debug(formatter, "Macro"),
            crate::ImplItem::Verbatim(v0) => {
                let mut formatter = formatter.debug_tuple("Verbatim");
                formatter.field(v0);
                formatter.finish()
            }
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::ImplItemConst {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "ImplItemConst")
    }
}
#[cfg(feature = "full")]
impl crate::ImplItemConst {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("attrs", &self.attrs);
        formatter.field("vis", &self.vis);
        formatter.field("defaultness", &self.defaultness);
        formatter.field("const_token", &self.const_token);
        formatter.field("ident", &self.ident);
        formatter.field("generics", &self.generics);
        formatter.field("colon_token", &self.colon_token);
        formatter.field("ty", &self.ty);
        formatter.field("eq_token", &self.eq_token);
        formatter.field("expr", &self.expr);
        formatter.field("semi_token", &self.semi_token);
        formatter.finish()
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::ImplItemFn {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "ImplItemFn")
    }
}
#[cfg(feature = "full")]
impl crate::ImplItemFn {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("attrs", &self.attrs);
        formatter.field("vis", &self.vis);
        formatter.field("defaultness", &self.defaultness);
        formatter.field("sig", &self.sig);
        formatter.field("block", &self.block);
        formatter.finish()
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::ImplItemMacro {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "ImplItemMacro")
    }
}
#[cfg(feature = "full")]
impl crate::ImplItemMacro {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("attrs", &self.attrs);
        formatter.field("mac", &self.mac);
        formatter.field("semi_token", &self.semi_token);
        formatter.finish()
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::ImplItemType {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "ImplItemType")
    }
}
#[cfg(feature = "full")]
impl crate::ImplItemType {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("attrs", &self.attrs);
        formatter.field("vis", &self.vis);
        formatter.field("defaultness", &self.defaultness);
        formatter.field("type_token", &self.type_token);
        formatter.field("ident", &self.ident);
        formatter.field("generics", &self.generics);
        formatter.field("eq_token", &self.eq_token);
        formatter.field("ty", &self.ty);
        formatter.field("semi_token", &self.semi_token);
        formatter.finish()
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::ImplRestriction {
    fn fmt(&self, _formatter: &mut fmt::Formatter) -> fmt::Result {
        match *self {}
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::Index {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        let mut formatter = formatter.debug_struct("Index");
        formatter.field("index", &self.index);
        formatter.field("span", &self.span);
        formatter.finish()
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::Item {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        formatter.write_str("Item::")?;
        match self {
            crate::Item::Const(v0) => v0.debug(formatter, "Const"),
            crate::Item::Enum(v0) => v0.debug(formatter, "Enum"),
            crate::Item::ExternCrate(v0) => v0.debug(formatter, "ExternCrate"),
            crate::Item::Fn(v0) => v0.debug(formatter, "Fn"),
            crate::Item::ForeignMod(v0) => v0.debug(formatter, "ForeignMod"),
            crate::Item::Impl(v0) => v0.debug(formatter, "Impl"),
            crate::Item::Macro(v0) => v0.debug(formatter, "Macro"),
            crate::Item::Mod(v0) => v0.debug(formatter, "Mod"),
            crate::Item::Static(v0) => v0.debug(formatter, "Static"),
            crate::Item::Struct(v0) => v0.debug(formatter, "Struct"),
            crate::Item::Trait(v0) => v0.debug(formatter, "Trait"),
            crate::Item::TraitAlias(v0) => v0.debug(formatter, "TraitAlias"),
            crate::Item::Type(v0) => v0.debug(formatter, "Type"),
            crate::Item::Union(v0) => v0.debug(formatter, "Union"),
            crate::Item::Use(v0) => v0.debug(formatter, "Use"),
            crate::Item::Verbatim(v0) => {
                let mut formatter = formatter.debug_tuple("Verbatim");
                formatter.field(v0);
                formatter.finish()
            }
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::ItemConst {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "ItemConst")
    }
}
#[cfg(feature = "full")]
impl crate::ItemConst {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("attrs", &self.attrs);
        formatter.field("vis", &self.vis);
        formatter.field("const_token", &self.const_token);
        formatter.field("ident", &self.ident);
        formatter.field("generics", &self.generics);
        formatter.field("colon_token", &self.colon_token);
        formatter.field("ty", &self.ty);
        formatter.field("eq_token", &self.eq_token);
        formatter.field("expr", &self.expr);
        formatter.field("semi_token", &self.semi_token);
        formatter.finish()
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::ItemEnum {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "ItemEnum")
    }
}
#[cfg(feature = "full")]
impl crate::ItemEnum {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("attrs", &self.attrs);
        formatter.field("vis", &self.vis);
        formatter.field("enum_token", &self.enum_token);
        formatter.field("ident", &self.ident);
        formatter.field("generics", &self.generics);
        formatter.field("brace_token", &self.brace_token);
        formatter.field("variants", &self.variants);
        formatter.finish()
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::ItemExternCrate {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "ItemExternCrate")
    }
}
#[cfg(feature = "full")]
impl crate::ItemExternCrate {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("attrs", &self.attrs);
        formatter.field("vis", &self.vis);
        formatter.field("extern_token", &self.extern_token);
        formatter.field("crate_token", &self.crate_token);
        formatter.field("ident", &self.ident);
        formatter.field("rename", &self.rename);
        formatter.field("semi_token", &self.semi_token);
        formatter.finish()
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::ItemFn {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "ItemFn")
    }
}
#[cfg(feature = "full")]
impl crate::ItemFn {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("attrs", &self.attrs);
        formatter.field("vis", &self.vis);
        formatter.field("sig", &self.sig);
        formatter.field("block", &self.block);
        formatter.finish()
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::ItemForeignMod {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "ItemForeignMod")
    }
}
#[cfg(feature = "full")]
impl crate::ItemForeignMod {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("attrs", &self.attrs);
        formatter.field("unsafety", &self.unsafety);
        formatter.field("abi", &self.abi);
        formatter.field("brace_token", &self.brace_token);
        formatter.field("items", &self.items);
        formatter.finish()
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::ItemImpl {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "ItemImpl")
    }
}
#[cfg(feature = "full")]
impl crate::ItemImpl {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("attrs", &self.attrs);
        formatter.field("defaultness", &self.defaultness);
        formatter.field("unsafety", &self.unsafety);
        formatter.field("impl_token", &self.impl_token);
        formatter.field("generics", &self.generics);
        formatter.field("trait_", &self.trait_);
        formatter.field("self_ty", &self.self_ty);
        formatter.field("brace_token", &self.brace_token);
        formatter.field("items", &self.items);
        formatter.finish()
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::ItemMacro {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "ItemMacro")
    }
}
#[cfg(feature = "full")]
impl crate::ItemMacro {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("attrs", &self.attrs);
        formatter.field("ident", &self.ident);
        formatter.field("mac", &self.mac);
        formatter.field("semi_token", &self.semi_token);
        formatter.finish()
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::ItemMod {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "ItemMod")
    }
}
#[cfg(feature = "full")]
impl crate::ItemMod {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("attrs", &self.attrs);
        formatter.field("vis", &self.vis);
        formatter.field("unsafety", &self.unsafety);
        formatter.field("mod_token", &self.mod_token);
        formatter.field("ident", &self.ident);
        formatter.field("content", &self.content);
        formatter.field("semi", &self.semi);
        formatter.finish()
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::ItemStatic {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "ItemStatic")
    }
}
#[cfg(feature = "full")]
impl crate::ItemStatic {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("attrs", &self.attrs);
        formatter.field("vis", &self.vis);
        formatter.field("static_token", &self.static_token);
        formatter.field("mutability", &self.mutability);
        formatter.field("ident", &self.ident);
        formatter.field("colon_token", &self.colon_token);
        formatter.field("ty", &self.ty);
        formatter.field("eq_token", &self.eq_token);
        formatter.field("expr", &self.expr);
        formatter.field("semi_token", &self.semi_token);
        formatter.finish()
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::ItemStruct {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "ItemStruct")
    }
}
#[cfg(feature = "full")]
impl crate::ItemStruct {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("attrs", &self.attrs);
        formatter.field("vis", &self.vis);
        formatter.field("struct_token", &self.struct_token);
        formatter.field("ident", &self.ident);
        formatter.field("generics", &self.generics);
        formatter.field("fields", &self.fields);
        formatter.field("semi_token", &self.semi_token);
        formatter.finish()
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::ItemTrait {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "ItemTrait")
    }
}
#[cfg(feature = "full")]
impl crate::ItemTrait {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("attrs", &self.attrs);
        formatter.field("vis", &self.vis);
        formatter.field("unsafety", &self.unsafety);
        formatter.field("auto_token", &self.auto_token);
        formatter.field("restriction", &self.restriction);
        formatter.field("trait_token", &self.trait_token);
        formatter.field("ident", &self.ident);
        formatter.field("generics", &self.generics);
        formatter.field("colon_token", &self.colon_token);
        formatter.field("supertraits", &self.supertraits);
        formatter.field("brace_token", &self.brace_token);
        formatter.field("items", &self.items);
        formatter.finish()
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::ItemTraitAlias {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "ItemTraitAlias")
    }
}
#[cfg(feature = "full")]
impl crate::ItemTraitAlias {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("attrs", &self.attrs);
        formatter.field("vis", &self.vis);
        formatter.field("trait_token", &self.trait_token);
        formatter.field("ident", &self.ident);
        formatter.field("generics", &self.generics);
        formatter.field("eq_token", &self.eq_token);
        formatter.field("bounds", &self.bounds);
        formatter.field("semi_token", &self.semi_token);
        formatter.finish()
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::ItemType {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "ItemType")
    }
}
#[cfg(feature = "full")]
impl crate::ItemType {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("attrs", &self.attrs);
        formatter.field("vis", &self.vis);
        formatter.field("type_token", &self.type_token);
        formatter.field("ident", &self.ident);
        formatter.field("generics", &self.generics);
        formatter.field("eq_token", &self.eq_token);
        formatter.field("ty", &self.ty);
        formatter.field("semi_token", &self.semi_token);
        formatter.finish()
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::ItemUnion {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "ItemUnion")
    }
}
#[cfg(feature = "full")]
impl crate::ItemUnion {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("attrs", &self.attrs);
        formatter.field("vis", &self.vis);
        formatter.field("union_token", &self.union_token);
        formatter.field("ident", &self.ident);
        formatter.field("generics", &self.generics);
        formatter.field("fields", &self.fields);
        formatter.finish()
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::ItemUse {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "ItemUse")
    }
}
#[cfg(feature = "full")]
impl crate::ItemUse {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("attrs", &self.attrs);
        formatter.field("vis", &self.vis);
        formatter.field("use_token", &self.use_token);
        formatter.field("leading_colon", &self.leading_colon);
        formatter.field("tree", &self.tree);
        formatter.field("semi_token", &self.semi_token);
        formatter.finish()
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::Label {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        let mut formatter = formatter.debug_struct("Label");
        formatter.field("name", &self.name);
        formatter.field("colon_token", &self.colon_token);
        formatter.finish()
    }
}
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::Lifetime {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "Lifetime")
    }
}
impl crate::Lifetime {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("apostrophe", &self.apostrophe);
        formatter.field("ident", &self.ident);
        formatter.finish()
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::LifetimeParam {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        let mut formatter = formatter.debug_struct("LifetimeParam");
        formatter.field("attrs", &self.attrs);
        formatter.field("lifetime", &self.lifetime);
        formatter.field("colon_token", &self.colon_token);
        formatter.field("bounds", &self.bounds);
        formatter.finish()
    }
}
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::Lit {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        formatter.write_str("Lit::")?;
        match self {
            crate::Lit::Str(v0) => v0.debug(formatter, "Str"),
            crate::Lit::ByteStr(v0) => v0.debug(formatter, "ByteStr"),
            crate::Lit::CStr(v0) => v0.debug(formatter, "CStr"),
            crate::Lit::Byte(v0) => v0.debug(formatter, "Byte"),
            crate::Lit::Char(v0) => v0.debug(formatter, "Char"),
            crate::Lit::Int(v0) => v0.debug(formatter, "Int"),
            crate::Lit::Float(v0) => v0.debug(formatter, "Float"),
            crate::Lit::Bool(v0) => v0.debug(formatter, "Bool"),
            crate::Lit::Verbatim(v0) => {
                let mut formatter = formatter.debug_tuple("Verbatim");
                formatter.field(v0);
                formatter.finish()
            }
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::Local {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "Local")
    }
}
#[cfg(feature = "full")]
impl crate::Local {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("attrs", &self.attrs);
        formatter.field("let_token", &self.let_token);
        formatter.field("pat", &self.pat);
        formatter.field("init", &self.init);
        formatter.field("semi_token", &self.semi_token);
        formatter.finish()
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::LocalInit {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        let mut formatter = formatter.debug_struct("LocalInit");
        formatter.field("eq_token", &self.eq_token);
        formatter.field("expr", &self.expr);
        formatter.field("diverge", &self.diverge);
        formatter.finish()
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::Macro {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        let mut formatter = formatter.debug_struct("Macro");
        formatter.field("path", &self.path);
        formatter.field("bang_token", &self.bang_token);
        formatter.field("delimiter", &self.delimiter);
        formatter.field("tokens", &self.tokens);
        formatter.finish()
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::MacroDelimiter {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        formatter.write_str("MacroDelimiter::")?;
        match self {
            crate::MacroDelimiter::Paren(v0) => {
                let mut formatter = formatter.debug_tuple("Paren");
                formatter.field(v0);
                formatter.finish()
            }
            crate::MacroDelimiter::Brace(v0) => {
                let mut formatter = formatter.debug_tuple("Brace");
                formatter.field(v0);
                formatter.finish()
            }
            crate::MacroDelimiter::Bracket(v0) => {
                let mut formatter = formatter.debug_tuple("Bracket");
                formatter.field(v0);
                formatter.finish()
            }
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::Member {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        formatter.write_str("Member::")?;
        match self {
            crate::Member::Named(v0) => {
                let mut formatter = formatter.debug_tuple("Named");
                formatter.field(v0);
                formatter.finish()
            }
            crate::Member::Unnamed(v0) => {
                let mut formatter = formatter.debug_tuple("Unnamed");
                formatter.field(v0);
                formatter.finish()
            }
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::Meta {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        formatter.write_str("Meta::")?;
        match self {
            crate::Meta::Path(v0) => v0.debug(formatter, "Path"),
            crate::Meta::List(v0) => v0.debug(formatter, "List"),
            crate::Meta::NameValue(v0) => v0.debug(formatter, "NameValue"),
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::MetaList {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "MetaList")
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
impl crate::MetaList {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("path", &self.path);
        formatter.field("delimiter", &self.delimiter);
        formatter.field("tokens", &self.tokens);
        formatter.finish()
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::MetaNameValue {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "MetaNameValue")
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
impl crate::MetaNameValue {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("path", &self.path);
        formatter.field("eq_token", &self.eq_token);
        formatter.field("value", &self.value);
        formatter.finish()
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::ParenthesizedGenericArguments {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "ParenthesizedGenericArguments")
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
impl crate::ParenthesizedGenericArguments {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("paren_token", &self.paren_token);
        formatter.field("inputs", &self.inputs);
        formatter.field("output", &self.output);
        formatter.finish()
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::Pat {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        formatter.write_str("Pat::")?;
        match self {
            crate::Pat::Const(v0) => v0.debug(formatter, "Const"),
            crate::Pat::Ident(v0) => v0.debug(formatter, "Ident"),
            crate::Pat::Lit(v0) => v0.debug(formatter, "Lit"),
            crate::Pat::Macro(v0) => v0.debug(formatter, "Macro"),
            crate::Pat::Or(v0) => v0.debug(formatter, "Or"),
            crate::Pat::Paren(v0) => v0.debug(formatter, "Paren"),
            crate::Pat::Path(v0) => v0.debug(formatter, "Path"),
            crate::Pat::Range(v0) => v0.debug(formatter, "Range"),
            crate::Pat::Reference(v0) => v0.debug(formatter, "Reference"),
            crate::Pat::Rest(v0) => v0.debug(formatter, "Rest"),
            crate::Pat::Slice(v0) => v0.debug(formatter, "Slice"),
            crate::Pat::Struct(v0) => v0.debug(formatter, "Struct"),
            crate::Pat::Tuple(v0) => v0.debug(formatter, "Tuple"),
            crate::Pat::TupleStruct(v0) => v0.debug(formatter, "TupleStruct"),
            crate::Pat::Type(v0) => v0.debug(formatter, "Type"),
            crate::Pat::Verbatim(v0) => {
                let mut formatter = formatter.debug_tuple("Verbatim");
                formatter.field(v0);
                formatter.finish()
            }
            crate::Pat::Wild(v0) => v0.debug(formatter, "Wild"),
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::PatIdent {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "PatIdent")
    }
}
#[cfg(feature = "full")]
impl crate::PatIdent {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("attrs", &self.attrs);
        formatter.field("by_ref", &self.by_ref);
        formatter.field("mutability", &self.mutability);
        formatter.field("ident", &self.ident);
        formatter.field("subpat", &self.subpat);
        formatter.finish()
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::PatOr {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "PatOr")
    }
}
#[cfg(feature = "full")]
impl crate::PatOr {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("attrs", &self.attrs);
        formatter.field("leading_vert", &self.leading_vert);
        formatter.field("cases", &self.cases);
        formatter.finish()
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::PatParen {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "PatParen")
    }
}
#[cfg(feature = "full")]
impl crate::PatParen {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("attrs", &self.attrs);
        formatter.field("paren_token", &self.paren_token);
        formatter.field("pat", &self.pat);
        formatter.finish()
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::PatReference {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "PatReference")
    }
}
#[cfg(feature = "full")]
impl crate::PatReference {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("attrs", &self.attrs);
        formatter.field("and_token", &self.and_token);
        formatter.field("mutability", &self.mutability);
        formatter.field("pat", &self.pat);
        formatter.finish()
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::PatRest {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "PatRest")
    }
}
#[cfg(feature = "full")]
impl crate::PatRest {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("attrs", &self.attrs);
        formatter.field("dot2_token", &self.dot2_token);
        formatter.finish()
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::PatSlice {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "PatSlice")
    }
}
#[cfg(feature = "full")]
impl crate::PatSlice {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("attrs", &self.attrs);
        formatter.field("bracket_token", &self.bracket_token);
        formatter.field("elems", &self.elems);
        formatter.finish()
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::PatStruct {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "PatStruct")
    }
}
#[cfg(feature = "full")]
impl crate::PatStruct {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("attrs", &self.attrs);
        formatter.field("qself", &self.qself);
        formatter.field("path", &self.path);
        formatter.field("brace_token", &self.brace_token);
        formatter.field("fields", &self.fields);
        formatter.field("rest", &self.rest);
        formatter.finish()
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::PatTuple {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "PatTuple")
    }
}
#[cfg(feature = "full")]
impl crate::PatTuple {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("attrs", &self.attrs);
        formatter.field("paren_token", &self.paren_token);
        formatter.field("elems", &self.elems);
        formatter.finish()
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::PatTupleStruct {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "PatTupleStruct")
    }
}
#[cfg(feature = "full")]
impl crate::PatTupleStruct {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("attrs", &self.attrs);
        formatter.field("qself", &self.qself);
        formatter.field("path", &self.path);
        formatter.field("paren_token", &self.paren_token);
        formatter.field("elems", &self.elems);
        formatter.finish()
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::PatType {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "PatType")
    }
}
#[cfg(feature = "full")]
impl crate::PatType {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("attrs", &self.attrs);
        formatter.field("pat", &self.pat);
        formatter.field("colon_token", &self.colon_token);
        formatter.field("ty", &self.ty);
        formatter.finish()
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::PatWild {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "PatWild")
    }
}
#[cfg(feature = "full")]
impl crate::PatWild {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("attrs", &self.attrs);
        formatter.field("underscore_token", &self.underscore_token);
        formatter.finish()
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::Path {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "Path")
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
impl crate::Path {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("leading_colon", &self.leading_colon);
        formatter.field("segments", &self.segments);
        formatter.finish()
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::PathArguments {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        formatter.write_str("PathArguments::")?;
        match self {
            crate::PathArguments::None => formatter.write_str("None"),
            crate::PathArguments::AngleBracketed(v0) => {
                v0.debug(formatter, "AngleBracketed")
            }
            crate::PathArguments::Parenthesized(v0) => {
                v0.debug(formatter, "Parenthesized")
            }
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::PathSegment {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        let mut formatter = formatter.debug_struct("PathSegment");
        formatter.field("ident", &self.ident);
        formatter.field("arguments", &self.arguments);
        formatter.finish()
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::PointerMutability {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        formatter.write_str("PointerMutability::")?;
        match self {
            crate::PointerMutability::Const(v0) => {
                let mut formatter = formatter.debug_tuple("Const");
                formatter.field(v0);
                formatter.finish()
            }
            crate::PointerMutability::Mut(v0) => {
                let mut formatter = formatter.debug_tuple("Mut");
                formatter.field(v0);
                formatter.finish()
            }
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::PreciseCapture {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        let mut formatter = formatter.debug_struct("PreciseCapture");
        formatter.field("use_token", &self.use_token);
        formatter.field("lt_token", &self.lt_token);
        formatter.field("params", &self.params);
        formatter.field("gt_token", &self.gt_token);
        formatter.finish()
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::PredicateLifetime {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        let mut formatter = formatter.debug_struct("PredicateLifetime");
        formatter.field("lifetime", &self.lifetime);
        formatter.field("colon_token", &self.colon_token);
        formatter.field("bounds", &self.bounds);
        formatter.finish()
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::PredicateType {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        let mut formatter = formatter.debug_struct("PredicateType");
        formatter.field("lifetimes", &self.lifetimes);
        formatter.field("bounded_ty", &self.bounded_ty);
        formatter.field("colon_token", &self.colon_token);
        formatter.field("bounds", &self.bounds);
        formatter.finish()
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::QSelf {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        let mut formatter = formatter.debug_struct("QSelf");
        formatter.field("lt_token", &self.lt_token);
        formatter.field("ty", &self.ty);
        formatter.field("position", &self.position);
        formatter.field("as_token", &self.as_token);
        formatter.field("gt_token", &self.gt_token);
        formatter.finish()
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::RangeLimits {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        formatter.write_str("RangeLimits::")?;
        match self {
            crate::RangeLimits::HalfOpen(v0) => {
                let mut formatter = formatter.debug_tuple("HalfOpen");
                formatter.field(v0);
                formatter.finish()
            }
            crate::RangeLimits::Closed(v0) => {
                let mut formatter = formatter.debug_tuple("Closed");
                formatter.field(v0);
                formatter.finish()
            }
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::Receiver {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        let mut formatter = formatter.debug_struct("Receiver");
        formatter.field("attrs", &self.attrs);
        formatter.field("reference", &self.reference);
        formatter.field("mutability", &self.mutability);
        formatter.field("self_token", &self.self_token);
        formatter.field("colon_token", &self.colon_token);
        formatter.field("ty", &self.ty);
        formatter.finish()
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::ReturnType {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        formatter.write_str("ReturnType::")?;
        match self {
            crate::ReturnType::Default => formatter.write_str("Default"),
            crate::ReturnType::Type(v0, v1) => {
                let mut formatter = formatter.debug_tuple("Type");
                formatter.field(v0);
                formatter.field(v1);
                formatter.finish()
            }
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::Signature {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        let mut formatter = formatter.debug_struct("Signature");
        formatter.field("constness", &self.constness);
        formatter.field("asyncness", &self.asyncness);
        formatter.field("unsafety", &self.unsafety);
        formatter.field("abi", &self.abi);
        formatter.field("fn_token", &self.fn_token);
        formatter.field("ident", &self.ident);
        formatter.field("generics", &self.generics);
        formatter.field("paren_token", &self.paren_token);
        formatter.field("inputs", &self.inputs);
        formatter.field("variadic", &self.variadic);
        formatter.field("output", &self.output);
        formatter.finish()
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::StaticMutability {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        formatter.write_str("StaticMutability::")?;
        match self {
            crate::StaticMutability::Mut(v0) => {
                let mut formatter = formatter.debug_tuple("Mut");
                formatter.field(v0);
                formatter.finish()
            }
            crate::StaticMutability::None => formatter.write_str("None"),
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::Stmt {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        formatter.write_str("Stmt::")?;
        match self {
            crate::Stmt::Local(v0) => v0.debug(formatter, "Local"),
            crate::Stmt::Item(v0) => {
                let mut formatter = formatter.debug_tuple("Item");
                formatter.field(v0);
                formatter.finish()
            }
            crate::Stmt::Expr(v0, v1) => {
                let mut formatter = formatter.debug_tuple("Expr");
                formatter.field(v0);
                formatter.field(v1);
                formatter.finish()
            }
            crate::Stmt::Macro(v0) => v0.debug(formatter, "Macro"),
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::StmtMacro {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "StmtMacro")
    }
}
#[cfg(feature = "full")]
impl crate::StmtMacro {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("attrs", &self.attrs);
        formatter.field("mac", &self.mac);
        formatter.field("semi_token", &self.semi_token);
        formatter.finish()
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::TraitBound {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        let mut formatter = formatter.debug_struct("TraitBound");
        formatter.field("paren_token", &self.paren_token);
        formatter.field("modifier", &self.modifier);
        formatter.field("lifetimes", &self.lifetimes);
        formatter.field("path", &self.path);
        formatter.finish()
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::TraitBoundModifier {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        formatter.write_str("TraitBoundModifier::")?;
        match self {
            crate::TraitBoundModifier::None => formatter.write_str("None"),
            crate::TraitBoundModifier::Maybe(v0) => {
                let mut formatter = formatter.debug_tuple("Maybe");
                formatter.field(v0);
                formatter.finish()
            }
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::TraitItem {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        formatter.write_str("TraitItem::")?;
        match self {
            crate::TraitItem::Const(v0) => v0.debug(formatter, "Const"),
            crate::TraitItem::Fn(v0) => v0.debug(formatter, "Fn"),
            crate::TraitItem::Type(v0) => v0.debug(formatter, "Type"),
            crate::TraitItem::Macro(v0) => v0.debug(formatter, "Macro"),
            crate::TraitItem::Verbatim(v0) => {
                let mut formatter = formatter.debug_tuple("Verbatim");
                formatter.field(v0);
                formatter.finish()
            }
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::TraitItemConst {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "TraitItemConst")
    }
}
#[cfg(feature = "full")]
impl crate::TraitItemConst {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("attrs", &self.attrs);
        formatter.field("const_token", &self.const_token);
        formatter.field("ident", &self.ident);
        formatter.field("generics", &self.generics);
        formatter.field("colon_token", &self.colon_token);
        formatter.field("ty", &self.ty);
        formatter.field("default", &self.default);
        formatter.field("semi_token", &self.semi_token);
        formatter.finish()
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::TraitItemFn {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "TraitItemFn")
    }
}
#[cfg(feature = "full")]
impl crate::TraitItemFn {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("attrs", &self.attrs);
        formatter.field("sig", &self.sig);
        formatter.field("default", &self.default);
        formatter.field("semi_token", &self.semi_token);
        formatter.finish()
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::TraitItemMacro {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "TraitItemMacro")
    }
}
#[cfg(feature = "full")]
impl crate::TraitItemMacro {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("attrs", &self.attrs);
        formatter.field("mac", &self.mac);
        formatter.field("semi_token", &self.semi_token);
        formatter.finish()
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::TraitItemType {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "TraitItemType")
    }
}
#[cfg(feature = "full")]
impl crate::TraitItemType {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("attrs", &self.attrs);
        formatter.field("type_token", &self.type_token);
        formatter.field("ident", &self.ident);
        formatter.field("generics", &self.generics);
        formatter.field("colon_token", &self.colon_token);
        formatter.field("bounds", &self.bounds);
        formatter.field("default", &self.default);
        formatter.field("semi_token", &self.semi_token);
        formatter.finish()
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::Type {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        formatter.write_str("Type::")?;
        match self {
            crate::Type::Array(v0) => v0.debug(formatter, "Array"),
            crate::Type::BareFn(v0) => v0.debug(formatter, "BareFn"),
            crate::Type::Group(v0) => v0.debug(formatter, "Group"),
            crate::Type::ImplTrait(v0) => v0.debug(formatter, "ImplTrait"),
            crate::Type::Infer(v0) => v0.debug(formatter, "Infer"),
            crate::Type::Macro(v0) => v0.debug(formatter, "Macro"),
            crate::Type::Never(v0) => v0.debug(formatter, "Never"),
            crate::Type::Paren(v0) => v0.debug(formatter, "Paren"),
            crate::Type::Path(v0) => v0.debug(formatter, "Path"),
            crate::Type::Ptr(v0) => v0.debug(formatter, "Ptr"),
            crate::Type::Reference(v0) => v0.debug(formatter, "Reference"),
            crate::Type::Slice(v0) => v0.debug(formatter, "Slice"),
            crate::Type::TraitObject(v0) => v0.debug(formatter, "TraitObject"),
            crate::Type::Tuple(v0) => v0.debug(formatter, "Tuple"),
            crate::Type::Verbatim(v0) => {
                let mut formatter = formatter.debug_tuple("Verbatim");
                formatter.field(v0);
                formatter.finish()
            }
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::TypeArray {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "TypeArray")
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
impl crate::TypeArray {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("bracket_token", &self.bracket_token);
        formatter.field("elem", &self.elem);
        formatter.field("semi_token", &self.semi_token);
        formatter.field("len", &self.len);
        formatter.finish()
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::TypeBareFn {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "TypeBareFn")
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
impl crate::TypeBareFn {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("lifetimes", &self.lifetimes);
        formatter.field("unsafety", &self.unsafety);
        formatter.field("abi", &self.abi);
        formatter.field("fn_token", &self.fn_token);
        formatter.field("paren_token", &self.paren_token);
        formatter.field("inputs", &self.inputs);
        formatter.field("variadic", &self.variadic);
        formatter.field("output", &self.output);
        formatter.finish()
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::TypeGroup {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "TypeGroup")
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
impl crate::TypeGroup {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("group_token", &self.group_token);
        formatter.field("elem", &self.elem);
        formatter.finish()
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::TypeImplTrait {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "TypeImplTrait")
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
impl crate::TypeImplTrait {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("impl_token", &self.impl_token);
        formatter.field("bounds", &self.bounds);
        formatter.finish()
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::TypeInfer {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "TypeInfer")
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
impl crate::TypeInfer {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("underscore_token", &self.underscore_token);
        formatter.finish()
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::TypeMacro {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "TypeMacro")
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
impl crate::TypeMacro {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("mac", &self.mac);
        formatter.finish()
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::TypeNever {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "TypeNever")
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
impl crate::TypeNever {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("bang_token", &self.bang_token);
        formatter.finish()
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::TypeParam {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        let mut formatter = formatter.debug_struct("TypeParam");
        formatter.field("attrs", &self.attrs);
        formatter.field("ident", &self.ident);
        formatter.field("colon_token", &self.colon_token);
        formatter.field("bounds", &self.bounds);
        formatter.field("eq_token", &self.eq_token);
        formatter.field("default", &self.default);
        formatter.finish()
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::TypeParamBound {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        formatter.write_str("TypeParamBound::")?;
        match self {
            crate::TypeParamBound::Trait(v0) => {
                let mut formatter = formatter.debug_tuple("Trait");
                formatter.field(v0);
                formatter.finish()
            }
            crate::TypeParamBound::Lifetime(v0) => v0.debug(formatter, "Lifetime"),
            #[cfg(feature = "full")]
            crate::TypeParamBound::PreciseCapture(v0) => {
                let mut formatter = formatter.debug_tuple("PreciseCapture");
                formatter.field(v0);
                formatter.finish()
            }
            crate::TypeParamBound::Verbatim(v0) => {
                let mut formatter = formatter.debug_tuple("Verbatim");
                formatter.field(v0);
                formatter.finish()
            }
            #[cfg(not(feature = "full"))]
            _ => unreachable!(),
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::TypeParen {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "TypeParen")
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
impl crate::TypeParen {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("paren_token", &self.paren_token);
        formatter.field("elem", &self.elem);
        formatter.finish()
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::TypePath {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "TypePath")
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
impl crate::TypePath {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("qself", &self.qself);
        formatter.field("path", &self.path);
        formatter.finish()
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::TypePtr {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "TypePtr")
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
impl crate::TypePtr {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("star_token", &self.star_token);
        formatter.field("const_token", &self.const_token);
        formatter.field("mutability", &self.mutability);
        formatter.field("elem", &self.elem);
        formatter.finish()
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::TypeReference {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "TypeReference")
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
impl crate::TypeReference {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("and_token", &self.and_token);
        formatter.field("lifetime", &self.lifetime);
        formatter.field("mutability", &self.mutability);
        formatter.field("elem", &self.elem);
        formatter.finish()
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::TypeSlice {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "TypeSlice")
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
impl crate::TypeSlice {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("bracket_token", &self.bracket_token);
        formatter.field("elem", &self.elem);
        formatter.finish()
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::TypeTraitObject {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "TypeTraitObject")
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
impl crate::TypeTraitObject {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("dyn_token", &self.dyn_token);
        formatter.field("bounds", &self.bounds);
        formatter.finish()
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::TypeTuple {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "TypeTuple")
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
impl crate::TypeTuple {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("paren_token", &self.paren_token);
        formatter.field("elems", &self.elems);
        formatter.finish()
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::UnOp {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        formatter.write_str("UnOp::")?;
        match self {
            crate::UnOp::Deref(v0) => {
                let mut formatter = formatter.debug_tuple("Deref");
                formatter.field(v0);
                formatter.finish()
            }
            crate::UnOp::Not(v0) => {
                let mut formatter = formatter.debug_tuple("Not");
                formatter.field(v0);
                formatter.finish()
            }
            crate::UnOp::Neg(v0) => {
                let mut formatter = formatter.debug_tuple("Neg");
                formatter.field(v0);
                formatter.finish()
            }
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::UseGlob {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        let mut formatter = formatter.debug_struct("UseGlob");
        formatter.field("star_token", &self.star_token);
        formatter.finish()
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::UseGroup {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        let mut formatter = formatter.debug_struct("UseGroup");
        formatter.field("brace_token", &self.brace_token);
        formatter.field("items", &self.items);
        formatter.finish()
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::UseName {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        let mut formatter = formatter.debug_struct("UseName");
        formatter.field("ident", &self.ident);
        formatter.finish()
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::UsePath {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        let mut formatter = formatter.debug_struct("UsePath");
        formatter.field("ident", &self.ident);
        formatter.field("colon2_token", &self.colon2_token);
        formatter.field("tree", &self.tree);
        formatter.finish()
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::UseRename {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        let mut formatter = formatter.debug_struct("UseRename");
        formatter.field("ident", &self.ident);
        formatter.field("as_token", &self.as_token);
        formatter.field("rename", &self.rename);
        formatter.finish()
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::UseTree {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        formatter.write_str("UseTree::")?;
        match self {
            crate::UseTree::Path(v0) => {
                let mut formatter = formatter.debug_tuple("Path");
                formatter.field(v0);
                formatter.finish()
            }
            crate::UseTree::Name(v0) => {
                let mut formatter = formatter.debug_tuple("Name");
                formatter.field(v0);
                formatter.finish()
            }
            crate::UseTree::Rename(v0) => {
                let mut formatter = formatter.debug_tuple("Rename");
                formatter.field(v0);
                formatter.finish()
            }
            crate::UseTree::Glob(v0) => {
                let mut formatter = formatter.debug_tuple("Glob");
                formatter.field(v0);
                formatter.finish()
            }
            crate::UseTree::Group(v0) => {
                let mut formatter = formatter.debug_tuple("Group");
                formatter.field(v0);
                formatter.finish()
            }
        }
    }
}
#[cfg(feature = "full")]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::Variadic {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        let mut formatter = formatter.debug_struct("Variadic");
        formatter.field("attrs", &self.attrs);
        formatter.field("pat", &self.pat);
        formatter.field("dots", &self.dots);
        formatter.field("comma", &self.comma);
        formatter.finish()
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::Variant {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        let mut formatter = formatter.debug_struct("Variant");
        formatter.field("attrs", &self.attrs);
        formatter.field("ident", &self.ident);
        formatter.field("fields", &self.fields);
        formatter.field("discriminant", &self.discriminant);
        formatter.finish()
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::VisRestricted {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        self.debug(formatter, "VisRestricted")
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
impl crate::VisRestricted {
    fn debug(&self, formatter: &mut fmt::Formatter, name: &str) -> fmt::Result {
        let mut formatter = formatter.debug_struct(name);
        formatter.field("pub_token", &self.pub_token);
        formatter.field("paren_token", &self.paren_token);
        formatter.field("in_token", &self.in_token);
        formatter.field("path", &self.path);
        formatter.finish()
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::Visibility {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        formatter.write_str("Visibility::")?;
        match self {
            crate::Visibility::Public(v0) => {
                let mut formatter = formatter.debug_tuple("Public");
                formatter.field(v0);
                formatter.finish()
            }
            crate::Visibility::Restricted(v0) => v0.debug(formatter, "Restricted"),
            crate::Visibility::Inherited => formatter.write_str("Inherited"),
        }
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::WhereClause {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        let mut formatter = formatter.debug_struct("WhereClause");
        formatter.field("where_token", &self.where_token);
        formatter.field("predicates", &self.predicates);
        formatter.finish()
    }
}
#[cfg(any(feature = "derive", feature = "full"))]
#[cfg_attr(docsrs, doc(cfg(feature = "extra-traits")))]
impl Debug for crate::WherePredicate {
    fn fmt(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        formatter.write_str("WherePredicate::")?;
        match self {
            crate::WherePredicate::Lifetime(v0) => {
                let mut formatter = formatter.debug_tuple("Lifetime");
                formatter.field(v0);
                formatter.finish()
            }
            crate::WherePredicate::Type(v0) => {
                let mut formatter = formatter.debug_tuple("Type");
                formatter.field(v0);
                formatter.finish()
            }
        }
    }
}
