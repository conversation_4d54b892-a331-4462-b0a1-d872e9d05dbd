/* yoshi-deluxe/src/errors.rs */
#![allow(clippy::items_after_statements)] // Yoshi-derive can generate items after statements.
#![allow(clippy::too_many_lines)] // This is a central error-handling module, which can be large.
#![allow(clippy::enum_variant_names)] // Naming convention is intentional for clarity.
#![allow(dead_code)] // Context fields are used by the Yoshi framework but appear unused to rustc.

//! **Brief:** Comprehensive, thematic error handling for the `yoshi-deluxe` crate using the `yoshi` framework.
//!
//! This module defines the core error types for the auto-correction system, fully leveraging
//! `yoshi-std` for its rich, structured, and context-aware error reporting, and `yoshi-derive`
//! for ergonomic, declarative error definitions. It embraces the thematic types of the Yoshi
//! ecosystem, such as `Oops`, `Hatch`, and `.lay()` to provide an expressive and robust error
//! handling experience with enhanced LSP integration.
//!
//! **Module Classification:** Core Framework Component
//! **Complexity Level:** Expert
//! **API Stability:** Stable
//!
//! ## Key Features
//!
//! - **Thematic Error Handling**: Uses `Hatch<T>` and `Oops` for an expressive API.
//! - **LSP Integration**: `yoshi_af!` macro provides comprehensive autofix capabilities.
//! - **Contextual Layering**: `.lay()` method for thematic error context attachment.
//! - **Rich Context**: Attaches structured payloads for deep diagnostics, not just string metadata.
//! - **Auto-Inference**: Leverages `yoshi-derive` to automatically infer error kinds from source fields.
//! - **Robust Factories**: Provides ergonomic factory functions using Hatch patterns.
//! - **Advanced Analysis**: Includes an `ErrorAnalyzer` for programmatic error inspection and reporting.
//! - **Asynchronous Recovery**: Offers `async` recovery strategies with retry logic using Hatch.
//!
// ~=####====A===r===c===M===o===o===n====S===t===u===d===i===o===s====X|0|$>
//! + Structured, derivable error types with automatic context injection and LSP integration.
//!  - Structured context payloads for type-safe diagnostic data.
//!  - Asynchronous, pattern-based error recovery strategies with `.lay()` context.
//!  - Enhanced LSP autofix integration via `yoshi_af!` macro.
// ~=####====A===r===c===M===o===o===n====S===t===u===d===i===o===s====X|0|$>
// **GitHub:** [ArcMoon Studios](https://github.com/arcmoonstudios)
// **Copyright:** (c) 2025 ArcMoon Studios
// **License:** MIT OR Apache-2.0
// **Contact:** <EMAIL>
// **Author:** Lord Xyn

use std::{any::Any, collections::HashMap, ops::Not, path::PathBuf, time::Duration};
use yoshi_derive::yoshi_af;
use yoshi_std::{Hatch, Yoshi, YoshiKind};

//--------------------------------------------------------------------------------------------------
// Core Error Types with Enhanced Yoshi Integration
//--------------------------------------------------------------------------------------------------

yoshi_af! {
    #[derive(Debug, YoshiError)]
    #[yoshi(
        default_severity = 128,
        namespace = "yoshi_deluxe",
        generate_helpers = true,
        auto_inference = true,
        strict_validation = true,
    )]
    pub enum AutoCorrectionError {
        /// Diagnostic processing failed with contextual information.
        #[yoshi(
            display = "Failed to process compiler diagnostic: {message}",
            kind = "Validation",
            severity = 160,
            suggestion = "Check cargo.toml format and run 'cargo check --message-format=json'",
            doc_url = "https://docs.rs/yoshi-deluxe/latest/yoshi_deluxe/errors/",
            category = "diagnostic_processing"
        )]
        DiagnosticProcessing {
            /// Error details.
            message: String,
            /// Source diagnostic data if available.
            #[yoshi(context = "diagnostic_data")]
            diagnostic_data: Option<String>,
            /// Project path context.
            #[yoshi(context = "project_path")]
            project_path: PathBuf,
            /// Cargo command that failed.
            #[yoshi(context = "cargo_command")]
            cargo_command: Option<String>,
        },

        /// AST analysis failed with precise location information.
        #[yoshi(
            display = "AST analysis failed at {file_path:?}:{line}:{column}: {reason}",
            kind = "Internal",
            severity = 180,
            suggestion = "Run 'rustfmt' to fix syntax issues and check for unclosed brackets",
            category = "ast_analysis"
        )]
        AstAnalysis {
            /// Reason for analysis failure.
            reason: String,
            /// Source file path.
            file_path: PathBuf,
            /// Line number (1-indexed).
            line: usize,
            /// Column number (1-indexed).
            column: usize,
            /// Byte offset if available.
            #[yoshi(context = "byte_offset")]
            byte_offset: Option<usize>,
            /// Details from the original `syn::Error`.
            #[yoshi(context = "ast_error_details")]
            ast_error_details: String,
            /// AST node type that failed.
            #[yoshi(context = "node_type")]
            node_type: Option<String>,
        },

        /// Documentation scraping encountered issues.
        #[yoshi(
            display = "Documentation scraping failed for {crate_name}::{type_name}: {error_type}",
            kind = "Network",
            severity = 120,
            transient,
            suggestion = "Verify internet connection and check if crate name is spelled correctly",
            category = "documentation"
        )]
        DocumentationScraping {
            /// Target crate name.
            crate_name: String,
            /// Target type name.
            type_name: String,
            /// Type of error encountered.
            error_type: String,
            /// HTTP status code if available.
            #[yoshi(context = "http_status")]
            http_status: Option<u16>,
            /// Underlying network error details.
            #[yoshi(context = "network_error_details")]
            network_error_details: String,
            /// Attempted URL.
            #[yoshi(context = "attempted_url")]
            attempted_url: Option<String>,
            /// Retry attempt number.
            #[yoshi(context = "retry_attempt")]
            retry_attempt: Option<usize>,
        },

        /// Code generation failed with correction context.
        #[yoshi(
            display = "Code generation failed for {correction_type}: {details}",
            kind = "Internal",
            severity = 200,
            suggestion = "Check template syntax and ensure all placeholders are properly formatted",
            category = "code_generation"
        )]
        CodeGeneration {
            /// Type of correction being attempted.
            correction_type: String,
            /// Specific failure details.
            details: String,
            /// Original problematic code.
            #[yoshi(context = "original_code")]
            original_code: String,
            /// Generation context metadata.
            #[yoshi(context = "generation_context")]
            generation_context: HashMap<String, String>,
            /// Confidence score when generation failed.
            #[yoshi(context = "confidence_score")]
            confidence_score: Option<f64>,
            /// Validation errors if any.
            #[yoshi(context = "validation_errors")]
            validation_errors: Option<Vec<String>>,
        },

        /// File operations failed with comprehensive context.
        #[yoshi(
            display = "File operation failed: {operation} on {file_path:?}",
            severity = 140,
            suggestion = "Verify file path exists and check read/write permissions",
            category = "file_operations"
        )]
        FileOperation {
            /// Type of file operation.
            operation: String,
            /// Target file path.
            file_path: PathBuf,
            /// File size if relevant.
            #[yoshi(context = "file_size")]
            file_size: Option<u64>,
            /// Underlying IO error.
            #[yoshi(source)]
            io_error: std::io::Error,
            /// Expected file permissions.
            #[yoshi(context = "expected_permissions")]
            expected_permissions: Option<String>,
            /// Actual file permissions.
            #[yoshi(context = "actual_permissions")]
            actual_permissions: Option<String>,
        },

        /// Configuration issues with system setup.
        #[yoshi(
            display = "Configuration error: {parameter} = '{value}' is invalid",
            kind = "Config",
            severity = 100,
            suggestion = "Check configuration file format and valid parameter ranges",
            category = "configuration"
        )]
        Configuration {
            /// Configuration parameter name.
            parameter: String,
            /// Invalid value.
            value: String,
            /// Expected value format.
            #[yoshi(context = "expected_format")]
            expected_format: Option<String>,
            /// Configuration source.
            #[yoshi(context = "config_source")]
            config_source: Option<String>,
            /// Validation rule that failed.
            #[yoshi(context = "validation_rule")]
            validation_rule: Option<String>,
        },

        /// Resource exhaustion errors.
        #[yoshi(
            display = "Resource exhausted: {resource_type} (limit: {limit}, requested: {requested})",
            kind = "ResourceExhausted",
            severity = 220,
            transient,
            suggestion = "Free up memory or increase resource limits in system configuration",
            category = "resource_management"
        )]
        ResourceExhausted {
            /// Type of resource.
            resource_type: String,
            /// Resource limit.
            limit: u64,
            /// Requested amount.
            requested: u64,
            /// Current usage.
            #[yoshi(context = "current_usage")]
            current_usage: Option<u64>,
            /// Resource pool identifier.
            #[yoshi(context = "resource_pool")]
            resource_pool: Option<String>,
        },

        /// Cache operation failures.
        #[yoshi(
            display = "Cache operation failed: {operation} for key '{cache_key}'",
            kind = "Internal",
            severity = 110,
            transient,
            suggestion = "Clear cache or increase cache size limits",
            category = "caching"
        )]
        CacheOperation {
            /// Cache operation type.
            operation: String,
            /// Cache key involved.
            cache_key: String,
            /// Cache type (docs, ast, etc.).
            #[yoshi(context = "cache_type")]
            cache_type: String,
            /// Cache size at time of failure.
            #[yoshi(context = "cache_size")]
            cache_size: Option<usize>,
            /// Error reason.
            reason: String,
        },

        /// Parsing and syntax errors.
        #[yoshi(
            display = "Parsing failed for {content_type}: {error_message}",
            kind = "Validation",
            severity = 150,
            suggestion = "Check input format matches expected schema or syntax",
            category = "parsing"
        )]
        ParsingFailure {
            /// Type of content being parsed.
            content_type: String,
            /// Parsing error message.
            error_message: String,
            /// Input content snippet.
            #[yoshi(context = "content_snippet")]
            content_snippet: Option<String>,
            /// Expected format.
            #[yoshi(context = "expected_format")]
            expected_format: Option<String>,
            /// Parser used.
            #[yoshi(context = "parser")]
            parser: Option<String>,
        },

        /// Timeout errors for long-running operations.
        #[yoshi(
            display = "Operation timed out: {operation} after {duration:?}",
            kind = "Timeout",
            severity = 130,
            transient,
            suggestion = "Increase timeout value or optimize the operation for better performance",
            category = "performance"
        )]
        OperationTimeout {
            /// Operation that timed out.
            operation: String,
            /// Actual duration before timeout.
            duration: Duration,
            /// Expected maximum duration.
            #[yoshi(context = "max_duration")]
            max_duration: Option<Duration>,
            /// Operation context.
            #[yoshi(context = "operation_context")]
            operation_context: Option<String>,
        },

        /// Version compatibility issues.
        #[yoshi(
            display = "Version compatibility error: {component} requires {required_version}, found {actual_version}",
            kind = "Validation",
            severity = 170,
            suggestion = "Run 'cargo update' or manually update dependency versions in Cargo.toml",
            category = "compatibility"
        )]
        VersionCompatibility {
            /// Component with version issue.
            component: String,
            /// Required version.
            required_version: String,
            /// Actual version found.
            actual_version: String,
            /// Compatibility rule.
            #[yoshi(context = "compatibility_rule")]
            compatibility_rule: Option<String>,
        },
    }
}

//--------------------------------------------------------------------------------------------------
// Note: From<reqwest::Error> for Yoshi implementation removed due to orphan rule violation
// Use AutoCorrectionError::DocumentationScraping variant for reqwest errors instead
//--------------------------------------------------------------------------------------------------

//--------------------------------------------------------------------------------------------------
// Thematic Result Type Aliases
//--------------------------------------------------------------------------------------------------

/// Thematic `Result` type alias for `yoshi-deluxe` operations, using `Hatch<T>`.
pub type Result<T> = Hatch<T>;

/// Specific result type for auto-correction operations.
pub type AutoCorrectionResult<T> = Hatch<T>;

/// Result type for asynchronous operations.
pub type AsyncResult<T> = Hatch<T>;

//--------------------------------------------------------------------------------------------------
// Structured Context Payloads
//--------------------------------------------------------------------------------------------------

/// Structured context for file-related operations.
#[derive(Debug, Clone)]
pub struct FileContext {
    /// The full path to the file.
    pub path: PathBuf,
    /// The name of the file.
    pub name: String,
    /// File metadata if available.
    pub metadata: Option<std::fs::Metadata>,
}

/// Structured context for performance-related data.
#[derive(Debug, Clone)]
pub struct PerformanceContext {
    /// The duration of the operation.
    pub duration: Duration,
    /// A performance category classification.
    pub category: &'static str,
    /// Performance threshold that was exceeded.
    pub threshold: Option<Duration>,
}

/// Structured context for correction-related data.
#[derive(Debug, Clone)]
pub struct CorrectionContext {
    /// The type of correction being applied.
    pub correction_type: String,
    /// The confidence score of the correction.
    pub confidence: f64,
    /// The correction strategy used.
    pub strategy: String,
}

/// Structured context for network operations.
#[derive(Debug, Clone)]
pub struct NetworkContext {
    /// The URL being accessed.
    pub url: String,
    /// HTTP method used.
    pub method: String,
    /// Response status if available.
    pub status: Option<u16>,
    /// Network latency.
    pub latency: Option<Duration>,
}

//--------------------------------------------------------------------------------------------------
// Error Enhancement Traits and Extensions
//--------------------------------------------------------------------------------------------------

/// Extension trait for enhancing errors with `yoshi-deluxe` specific, structured context.
///
/// This trait provides methods to attach type-safe, structured payloads to errors,
/// promoting robust diagnostics over simple string metadata, using `Hatch<T>` patterns.
pub trait YoshiDeluxeExt<T, E> {
    /// Attaches a structured `FileContext` payload to the error.
    fn with_file_context(self, file_path: &std::path::Path) -> Hatch<T>;

    /// Attaches a structured `PerformanceContext` payload to the error.
    fn with_performance_context(self, duration: Duration) -> Hatch<T>;

    /// Attaches a structured `CorrectionContext` payload to the error.
    fn with_correction_context(self, correction_type: &str, confidence: f64) -> Hatch<T>;

    /// Attaches a structured `NetworkContext` payload to the error.
    fn with_network_context(self, url: &str, method: &str) -> Hatch<T>;

    /// Adds thematic context using `.lay()` with structured payload.
    fn lay_with_context<C>(self, message: impl Into<String>, context: C) -> Hatch<T>
    where
        C: Any + Send + Sync + 'static;
}

impl<T, E> YoshiDeluxeExt<T, E> for std::result::Result<T, E>
where
    E: Into<Yoshi>,
{
    /// Attaches a `FileContext` payload to an error, converting it to a `Hatch<T>`.
    fn with_file_context(self, file_path: &std::path::Path) -> Hatch<T> {
        self.map_err(|e| {
            let metadata = std::fs::metadata(file_path).ok();
            let context = FileContext {
                path: file_path.to_path_buf(),
                name: file_path
                    .file_name()
                    .and_then(std::ffi::OsStr::to_str)
                    .unwrap_or("unknown")
                    .to_string(),
                metadata,
            };
            e.into().with_shell(context).lay(format!(
                "File operation failed for: {}",
                file_path.display()
            ))
        })
    }

    /// Attaches a `PerformanceContext` payload to an error, converting it to a `Hatch<T>`.
    fn with_performance_context(self, duration: Duration) -> Hatch<T> {
        self.map_err(|e| {
            let category = if duration.as_millis() > 1000 {
                "slow"
            } else if duration.as_millis() > 100 {
                "medium"
            } else {
                "fast"
            };
            let threshold = Some(Duration::from_millis(100));
            let context = PerformanceContext {
                duration,
                category,
                threshold,
            };
            e.into()
                .with_shell(context)
                .lay(format!("Performance degradation detected: {duration:?}"))
        })
    }

    /// Attaches a `CorrectionContext` payload to an error, converting it to a `Hatch<T>`.
    fn with_correction_context(self, correction_type: &str, confidence: f64) -> Hatch<T> {
        self.map_err(|e| {
            let strategy = if confidence > 0.8 {
                "high_confidence"
            } else if confidence > 0.5 {
                "medium_confidence"
            } else {
                "low_confidence"
            };
            let context = CorrectionContext {
                correction_type: correction_type.to_string(),
                confidence,
                strategy: strategy.to_string(),
            };
            e.into().with_shell(context).lay(format!(
                "Correction failed for {correction_type} (confidence: {confidence:.2})"
            ))
        })
    }

    /// Attaches a `NetworkContext` payload to an error, converting it to a `Hatch<T>`.
    fn with_network_context(self, url: &str, method: &str) -> Hatch<T> {
        self.map_err(|e| {
            let context = NetworkContext {
                url: url.to_string(),
                method: method.to_string(),
                status: None,
                latency: None,
            };
            e.into()
                .with_shell(context)
                .lay(format!("Network operation failed: {method} {url}"))
        })
    }

    /// Adds thematic context using `.lay()` with structured payload.
    fn lay_with_context<C>(self, message: impl Into<String>, context: C) -> Hatch<T>
    where
        C: Any + Send + Sync + 'static,
    {
        self.map_err(|e| e.into().with_shell(context).lay(message))
    }
}

//--------------------------------------------------------------------------------------------------
// Error Factories for Common Patterns
//--------------------------------------------------------------------------------------------------

/// Factory functions for creating common `Hatch<T>` errors with proper context.
pub mod factory {
    use super::*;

    /// Creates a diagnostic processing error with thematic context.
    pub fn diagnostic_processing_error(
        message: impl Into<String>,
        project_path: impl Into<PathBuf>,
    ) -> Yoshi {
        let msg = message.into();
        let path = project_path.into();

        Yoshi::from(format!("Failed to process compiler diagnostic: {}", msg))
            .with_metadata("project_path", path.display().to_string())
            .with_metadata("error_type", "diagnostic_processing")
            .lay("Diagnostic processing pipeline encountered an error")
    }

    /// Creates an AST analysis error with enhanced context.
    pub fn ast_analysis_error(
        reason: impl Into<String>,
        file_path: impl Into<PathBuf>,
        line: usize,
        column: usize,
        source_error: syn::Error,
    ) -> Yoshi {
        let file_path = file_path.into();
        let error: Yoshi = Yoshi::new(YoshiKind::Internal {
            message: format!("AST analysis failed: {}", reason.into()).into(),
            source: None,
            component: Some("ast_analyzer".into()),
        })
        .with_metadata("file_path", file_path.display().to_string())
        .with_metadata("line", line.to_string())
        .with_metadata("column", column.to_string())
        .with_metadata("ast_error_details", source_error.to_string());
        error.lay(format!("AST analysis failed in {}", file_path.display()))
    }

    /// Creates a documentation scraping error with network context.
    pub fn docs_scraping_error(
        crate_name: impl Into<String>,
        type_name: impl Into<String>,
        error_type: impl Into<String>,
        network_error: reqwest::Error,
    ) -> Yoshi {
        let crate_name = crate_name.into();
        let type_name = type_name.into();
        let error: Yoshi = Yoshi::new(YoshiKind::Network {
            message: format!(
                "Documentation scraping failed for {}::{}",
                crate_name, type_name
            )
            .into(),
            source: None,
            error_code: network_error.status().map(|s| s.as_u16() as u32),
        })
        .with_metadata("crate_name", crate_name.clone())
        .with_metadata("type_name", type_name.clone())
        .with_metadata("error_type", error_type.into())
        .with_metadata("network_error_details", network_error.to_string());
        error.lay(format!(
            "Documentation scraping failed for {crate_name}::{type_name}"
        ))
    }

    /// Creates a code generation error with generation context.
    pub fn code_generation_error(
        correction_type: impl Into<String>,
        details: impl Into<String>,
        original_code: impl Into<String>,
    ) -> Yoshi {
        let correction_type = correction_type.into();
        let error: Yoshi = Yoshi::new(YoshiKind::Internal {
            message: format!("Code generation failed for {}", correction_type).into(),
            source: None,
            component: Some("code_generator".into()),
        })
        .with_metadata("correction_type", correction_type.clone())
        .with_metadata("details", details.into())
        .with_metadata("original_code", original_code.into());
        error.lay(format!(
            "Code generation pipeline failed for {correction_type}"
        ))
    }

    /// Creates a file operation error with file context.
    pub fn file_operation_error(
        operation: impl Into<String>,
        file_path: impl Into<PathBuf>,
        io_error: std::io::Error,
    ) -> Yoshi {
        let operation = operation.into();
        let file_path = file_path.into();
        let error: Yoshi = Yoshi::new(YoshiKind::Io(io_error))
            .with_metadata("operation", operation.clone())
            .with_metadata("file_path", file_path.display().to_string());
        error.lay(format!(
            "File {operation} operation failed for {}",
            file_path.display()
        ))
    }

    /// Creates a configuration error with validation context.
    pub fn configuration_error(parameter: impl Into<String>, value: impl Into<String>) -> Yoshi {
        let parameter = parameter.into();
        let value = value.into();
        let error: Yoshi = Yoshi::new(YoshiKind::Config {
            message: format!(
                "Configuration validation failed for {}={}",
                parameter, value
            )
            .into(),
            source: None,
            config_path: None,
        })
        .with_metadata("parameter", parameter.clone())
        .with_metadata("value", value.clone());
        error.lay(format!(
            "Configuration validation failed for {parameter}={value}"
        ))
    }

    /// Creates a resource exhausted error with resource context.
    pub fn resource_exhausted_error(
        resource_type: impl Into<String>,
        limit: u64,
        requested: u64,
    ) -> Yoshi {
        let resource_type = resource_type.into();
        let error: Yoshi = Yoshi::new(YoshiKind::ResourceExhausted {
            resource: resource_type.clone(),
            limit: limit.to_string(),
            current: requested.to_string(),
            usage_percentage: Some((requested as f64 / limit as f64) * 100.0),
        });
        error.lay(format!("Resource exhaustion detected for {resource_type}"))
    }

    /// Creates a timeout error with operation context.
    pub fn timeout_error(operation: impl Into<String>, duration: Duration) -> Yoshi {
        let operation = operation.into();
        let error: Yoshi = Yoshi::new(YoshiKind::Timeout {
            operation: operation.clone(),
            duration,
            expected_max: None,
        });
        error.lay(format!("Operation timeout occurred for {operation}"))
    }

    /// Creates a cache operation error with cache context.
    pub fn cache_operation_error(
        operation: impl Into<String>,
        cache_key: impl Into<String>,
        reason: impl Into<String>,
    ) -> Yoshi {
        let operation = operation.into();
        let cache_key = cache_key.into();
        let error: Yoshi = Yoshi::new(YoshiKind::Internal {
            message: format!(
                "Cache {} operation failed for key: {}",
                operation, cache_key
            ),
            source: None,
            component: Some("cache_manager".into()),
        })
        .with_metadata("operation", operation.clone())
        .with_metadata("cache_key", cache_key.clone())
        .with_metadata("cache_type", "general")
        .with_metadata("reason", reason.into());
        error.lay(format!("Cache {operation} failed for key: {cache_key}"))
    }
}

//--------------------------------------------------------------------------------------------------
// Error Analysis and Reporting
//--------------------------------------------------------------------------------------------------

/// Analyzes `Yoshi` errors to provide insights for reporting and recovery.
pub struct ErrorAnalyzer;

impl ErrorAnalyzer {
    /// Analyzes an error and provides a structured report.
    #[must_use]
    pub fn analyze_error(error: &Yoshi) -> ErrorAnalysis {
        ErrorAnalysis {
            category: Self::categorize_error(error),
            severity_level: Self::assess_severity(error),
            recovery_suggestions: Self::generate_recovery_suggestions(error),
            is_transient: error.is_transient(),
            error_pattern: Self::identify_pattern(error),
            context_analysis: Self::analyze_context(error),
            autofix_availability: Self::check_autofix_availability(error),
        }
    }

    fn categorize_error(yoshi: &Yoshi) -> String {
        match yoshi.kind() {
            YoshiKind::Io(_) => "File System".to_string(),
            YoshiKind::Network { .. } => "Network".to_string(),
            YoshiKind::Config { .. } => "Configuration".to_string(),
            YoshiKind::Validation { .. } => "Validation".to_string(),
            YoshiKind::Internal { .. } => "Internal Logic".to_string(),
            YoshiKind::NotFound { .. } => "Resource Not Found".to_string(),
            YoshiKind::Timeout { .. } => "Performance".to_string(),
            YoshiKind::ResourceExhausted { .. } => "Resource Management".to_string(),
            YoshiKind::Security { .. } => "Security".to_string(),
            YoshiKind::Foreign {
                error_type_name, ..
            } => {
                format!("Foreign Error ({})", error_type_name)
            }
            YoshiKind::Multiple { .. } => "Multiple Errors".to_string(),
            _ => "Unknown".to_string(),
        }
    }

    fn assess_severity(yoshi: &Yoshi) -> SeverityLevel {
        match yoshi.severity() {
            0..=79 => SeverityLevel::Low,
            80..=149 => SeverityLevel::Medium,
            150..=219 => SeverityLevel::High,
            220..=255 => SeverityLevel::Critical,
        }
    }

    fn generate_recovery_suggestions(yoshi: &Yoshi) -> Vec<String> {
        let mut suggestions = Vec::new();

        if let Some(suggestion) = yoshi.suggestion() {
            suggestions.push(suggestion.to_string());
        }

        match yoshi.kind() {
            YoshiKind::Network { .. } => {
                suggestions.push("Check network connectivity and firewall settings".to_string());
                suggestions.push("Verify proxy configuration if applicable".to_string());
            }
            YoshiKind::Io(_) => {
                suggestions.push("Verify file path and permissions".to_string());
                suggestions.push("Check available disk space".to_string());
            }
            YoshiKind::Timeout { .. } => {
                suggestions
                    .push("Increase timeout configuration or optimize the operation".to_string());
                suggestions
                    .push("Consider implementing retry logic with exponential backoff".to_string());
            }
            YoshiKind::ResourceExhausted { .. } => {
                suggestions.push("Free up system resources or increase limits".to_string());
                suggestions.push("Implement resource pooling or cleanup strategies".to_string());
            }
            YoshiKind::Config { .. } => {
                suggestions.push("Review configuration file format and syntax".to_string());
                suggestions.push("Validate configuration against schema".to_string());
            }
            _ => {}
        }

        if suggestions.is_empty() {
            suggestions
                .push("Review error details and system logs for more information".to_string());
        }
        suggestions
    }

    fn identify_pattern(yoshi: &Yoshi) -> String {
        let error_str = yoshi.to_string();
        if error_str.contains("mismatched types") {
            "Type Mismatch".to_string()
        } else if error_str.contains("borrow of moved value") {
            "Ownership Error".to_string()
        } else if error_str.contains("Connection refused") {
            "Network Connection".to_string()
        } else if error_str.contains("Permission denied") {
            "File Permission".to_string()
        } else if error_str.contains("timeout") || error_str.contains("timed out") {
            "Timeout Error".to_string()
        } else if error_str.contains("not found") {
            "Resource Not Found".to_string()
        } else {
            "Generic".to_string()
        }
    }

    fn analyze_context(yoshi: &Yoshi) -> ContextAnalysis {
        let analysis = yoshi.analyze_contexts();
        ContextAnalysis {
            context_count: analysis.total_contexts,
            has_structured_payloads: analysis.typed_payloads > 0,
            has_location_info: analysis.has_location_info,
            metadata_richness: if analysis.metadata_entries > 5 {
                "Rich"
            } else if analysis.metadata_entries > 2 {
                "Moderate"
            } else {
                "Basic"
            }
            .to_string(),
        }
    }

    fn check_autofix_availability(yoshi: &Yoshi) -> bool {
        yoshi.auto_fixes().is_empty().not()
    }
}

/// A structured analysis result for a given error.
#[derive(Debug, Clone)]
pub struct ErrorAnalysis {
    /// A high-level category for the error.
    pub category: String,
    /// An assessed severity level.
    pub severity_level: SeverityLevel,
    /// A list of potential recovery suggestions.
    pub recovery_suggestions: Vec<String>,
    /// Indicates if the error is likely transient.
    pub is_transient: bool,
    /// The specific error pattern identified.
    pub error_pattern: String,
    /// Analysis of error context richness.
    pub context_analysis: ContextAnalysis,
    /// Whether autofix suggestions are available.
    pub autofix_availability: bool,
}

/// Analysis of error context information.
#[derive(Debug, Clone)]
pub struct ContextAnalysis {
    /// Number of context objects attached.
    pub context_count: usize,
    /// Whether structured payloads are present.
    pub has_structured_payloads: bool,
    /// Whether location information is available.
    pub has_location_info: bool,
    /// Richness of metadata information.
    pub metadata_richness: String,
}

/// Enumeration of assessed severity levels.
#[derive(Debug, Clone, PartialEq, Eq, PartialOrd, Ord)]
pub enum SeverityLevel {
    /// Low severity (e.g., informational).
    Low,
    /// Medium severity (e.g., warning).
    Medium,
    /// High severity (e.g., recoverable error).
    High,
    /// Critical severity (e.g., unrecoverable error, crash).
    Critical,
}

//--------------------------------------------------------------------------------------------------
// Error Recovery Strategies
//--------------------------------------------------------------------------------------------------

/// Provides asynchronous error recovery strategies using `Hatch<T>` patterns.
pub mod recovery {
    use super::*;
    use std::sync::OnceLock;
    use std::time::Instant;
    use tokio::sync::Mutex;

    /// Retries an `async` operation with exponential backoff if it fails with a transient error.
    pub async fn attempt_recovery<T, F, Fut>(mut operation: F, max_retries: u32) -> Hatch<T>
    where
        F: FnMut() -> Fut,
        Fut: std::future::Future<Output = Hatch<T>>,
    {
        for attempt in 0..max_retries {
            match operation().await {
                Ok(result) => return Ok(result),
                Err(error) => {
                    if !error.is_transient() {
                        return Err(
                            error.lay(format!("Non-transient error after {attempt} attempts"))
                        );
                    }
                    if attempt == max_retries - 1 {
                        return Err(error.lay("Maximum retries exceeded for transient error"));
                    }
                    let delay = Duration::from_millis(100 * 2_u64.pow(attempt));
                    tokio::time::sleep(delay).await;
                }
            }
        }

        Err(factory::configuration_error(
            "recovery_operation",
            "Exited retry loop unexpectedly",
        ))
    }

    /// Retries an `async` operation if the error message contains specific patterns.
    pub async fn retry_on_pattern<T, F, Fut>(
        mut operation: F,
        max_retries: usize,
        retry_patterns: &[&str],
    ) -> Hatch<T>
    where
        F: FnMut() -> Fut,
        Fut: std::future::Future<Output = Hatch<T>>,
    {
        for attempt in 0..=max_retries {
            match operation().await {
                Ok(result) => return Ok(result),
                Err(error) => {
                    let error_str = error.to_string().to_lowercase();
                    let should_retry = retry_patterns
                        .iter()
                        .any(|pattern| error_str.contains(&pattern.to_lowercase()));

                    if !should_retry || attempt == max_retries {
                        return Err(error.lay(format!(
                            "Pattern-based retry failed after {attempt} attempts"
                        )));
                    }

                    let delay = Duration::from_millis(200 * (attempt + 1) as u64);
                    tokio::time::sleep(delay).await;
                }
            }
        }
        unreachable!()
    }

    struct CircuitBreakerState {
        failure_count: u32,
        last_failure: Option<Instant>,
    }

    static CIRCUIT_BREAKER: OnceLock<Mutex<CircuitBreakerState>> = OnceLock::new();

    fn get_breaker() -> &'static Mutex<CircuitBreakerState> {
        CIRCUIT_BREAKER.get_or_init(|| {
            Mutex::new(CircuitBreakerState {
                failure_count: 0,
                last_failure: None,
            })
        })
    }

    /// Advanced recovery with circuit breaker pattern.
    pub async fn circuit_breaker_recovery<T, F, Fut>(
        mut operation: F,
        failure_threshold: u32,
        recovery_timeout: Duration,
    ) -> Hatch<T>
    where
        F: FnMut() -> Fut,
        Fut: std::future::Future<Output = Hatch<T>>,
    {
        let breaker = get_breaker();
        let mut guard = breaker.lock().await;

        if let Some(last_failure) = guard.last_failure {
            if guard.failure_count >= failure_threshold && last_failure.elapsed() < recovery_timeout
            {
                return Err(
                    factory::timeout_error("circuit_breaker", last_failure.elapsed())
                        .lay("Circuit breaker is open, operation blocked"),
                );
            }
        }
        drop(guard);

        match operation().await {
            Ok(result) => {
                let mut guard = breaker.lock().await;
                guard.failure_count = 0;
                guard.last_failure = None;
                Ok(result)
            }
            Err(error) => {
                let mut guard = breaker.lock().await;
                guard.failure_count += 1;
                guard.last_failure = Some(Instant::now());
                Err(error.lay(format!(
                    "Circuit breaker failure count: {}",
                    guard.failure_count
                )))
            }
        }
    }

    /// Graceful degradation with fallback strategies.
    pub async fn graceful_degradation<T, F1, F2, Fut1, Fut2>(
        primary_operation: F1,
        fallback_operation: F2,
    ) -> Hatch<T>
    where
        F1: FnOnce() -> Fut1,
        F2: FnOnce() -> Fut2,
        Fut1: std::future::Future<Output = Hatch<T>>,
        Fut2: std::future::Future<Output = Hatch<T>>,
    {
        match primary_operation().await {
            Ok(result) => Ok(result),
            Err(primary_error) => match fallback_operation().await {
                Ok(fallback_result) => Ok(fallback_result),
                Err(fallback_error) => Err(primary_error
                    .lay("Primary operation failed")
                    .with_shell(fallback_error)
                    .lay("Fallback operation also failed")),
            },
        }
    }
}

//--------------------------------------------------------------------------------------------------
// Enhanced Error Utilities
//--------------------------------------------------------------------------------------------------

/// Utility functions for error handling and conversion.
pub mod utils {
    use super::*;

    /// Converts a standard `Result` to a `Hatch<T>` with thematic context.
    pub fn into_hatch<T, E>(result: std::result::Result<T, E>, context: &str) -> Hatch<T>
    where
        E: Into<Yoshi>,
    {
        result.map_err(|e| e.into().lay(context))
    }

    /// Wraps multiple errors into a single aggregated error.
    pub fn aggregate_errors<T>(mut errors: Vec<Yoshi>, context: &str) -> Hatch<T> {
        if errors.is_empty() {
            return Err(
                factory::configuration_error("aggregate_errors", "No errors provided").lay(context),
            );
        }

        if errors.len() == 1 {
            return Err(errors.remove(0).lay(context));
        }

        let multi_error = Yoshi::new(YoshiKind::Multiple {
            errors,
            primary_index: Some(0), // Assume the first error is primary
        });

        Err(multi_error.lay(context))
    }

    /// Creates a detailed error report from a Yoshi error.
    pub fn create_error_report(error: &Yoshi) -> String {
        let analysis = ErrorAnalyzer::analyze_error(error);
        format!(
            "Error Report:\n\
             Category: {}\n\
             Severity: {:?}\n\
             Pattern: {}\n\
             Transient: {}\n\
             Autofix Available: {}\n\
             Recovery Suggestions:\n  - {}\n\
             Full Error: {}",
            analysis.category,
            analysis.severity_level,
            analysis.error_pattern,
            analysis.is_transient,
            analysis.autofix_availability,
            analysis.recovery_suggestions.join("\n  - "),
            error
        )
    }
}

//--------------------------------------------------------------------------------------------------
// Public Re-exports
//--------------------------------------------------------------------------------------------------

// Re-export common factory functions for convenience
pub use factory::{
    ast_analysis_error, cache_operation_error, code_generation_error, configuration_error,
    diagnostic_processing_error, docs_scraping_error, file_operation_error,
    resource_exhausted_error, timeout_error,
};

// Re-export utility modules
pub use utils::*;

// Note: Context types (CorrectionContext, FileContext, PerformanceContext) are already made public by the yoshi_af! macro

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_factory_error_creation_with_lay_context() {
        let yoshi_err =
            factory::diagnostic_processing_error("Test diagnostic error", "/tmp/test-project");

        assert!(yoshi_err.to_string().contains("Test diagnostic error"));
        assert!(yoshi_err
            .to_string()
            .contains("Diagnostic processing pipeline encountered an error"));
        assert_eq!(yoshi_err.severity(), 160);
    }

    #[test]
    fn test_yoshi_deluxe_ext_with_structured_payload_and_lay() {
        let result: std::result::Result<(), _> = Err(std::io::Error::new(
            std::io::ErrorKind::NotFound,
            "File not found",
        ));

        let enhanced_result: Hatch<()> =
            result.with_file_context(std::path::Path::new("/tmp/test.rs"));
        assert!(enhanced_result.is_err());

        let yoshi_err = enhanced_result.unwrap_err();

        // Check for the structured payload
        let file_ctx = yoshi_err
            .shell::<FileContext>()
            .expect("FileContext payload should exist");
        assert_eq!(file_ctx.path, PathBuf::from("/tmp/test.rs"));
        assert_eq!(file_ctx.name, "test.rs");

        // Check for thematic context from .lay()
        assert!(yoshi_err
            .to_string()
            .contains("File operation failed for: /tmp/test.rs"));
    }

    #[test]
    fn test_error_analyzer_with_enhanced_analysis() {
        let yoshi_err = factory::timeout_error("database_query", Duration::from_secs(30));
        let analysis = ErrorAnalyzer::analyze_error(&yoshi_err);

        assert_eq!(analysis.category, "Performance");
        assert_eq!(analysis.severity_level, SeverityLevel::Medium);
        assert!(analysis.is_transient);
        assert!(analysis.recovery_suggestions.len() > 1);
        assert_eq!(analysis.error_pattern, "Timeout Error");
    }

    #[test]
    fn test_yoshi_af_autofix_integration() {
        let err: AutoCorrectionError = AutoCorrectionError::AstAnalysis {
            reason: "Unexpected token".to_string(),
            file_path: "/src/main.rs".into(),
            line: 10,
            column: 5,
            byte_offset: None,
            ast_error_details: "test error".to_string(),
            node_type: None,
        };

        // Check that autofix suggestions are available BEFORE converting to Yoshi
        let auto_fixes = err.quick_fixes();
        assert!(
            !auto_fixes.is_empty(),
            "Should have autofix suggestions from yoshi_af!"
        );

        let yoshi_err: Yoshi = err.into();

        // Check that autofix suggestions are available
        let auto_fixes = yoshi_err.quick_fixes();
        assert!(
            !auto_fixes.is_empty(),
            "Should have autofix suggestions from yoshi_af!"
        );
    }

    #[test]
    fn test_lay_with_context_extension() {
        let result: std::result::Result<(), &str> = Err("test error");
        let correction_ctx = CorrectionContext {
            correction_type: "syntax_fix".to_string(),
            confidence: 0.85,
            strategy: "high_confidence".to_string(),
        };

        let enhanced: Hatch<()> =
            result.lay_with_context("Syntax correction failed", correction_ctx);
        assert!(enhanced.is_err());

        let yoshi_err = enhanced.unwrap_err();
        assert!(yoshi_err.to_string().contains("Syntax correction failed"));

        let ctx = yoshi_err
            .shell::<CorrectionContext>()
            .expect("Should have CorrectionContext");
        assert_eq!(ctx.correction_type, "syntax_fix");
        assert_eq!(ctx.confidence, 0.85);
    }

    #[tokio::test]
    async fn test_enhanced_async_recovery_with_lay() {
        let mut attempt_count = 0;
        let result = recovery::attempt_recovery(
            || {
                attempt_count += 1;
                async move {
                    if attempt_count < 3 {
                        Err(factory::timeout_error("test_op", Duration::from_millis(10)))
                    } else {
                        Ok(42)
                    }
                }
            },
            5,
        )
        .await;

        assert!(result.is_ok());
        assert_eq!(result.unwrap(), 42);
        assert_eq!(attempt_count, 3);
    }

    #[tokio::test]
    async fn test_graceful_degradation_recovery() {
        let primary_op =
            || async { Err(factory::timeout_error("primary", Duration::from_secs(1))) };

        let fallback_op = || async { Ok("fallback_result") };

        let result = recovery::graceful_degradation(primary_op, fallback_op).await;
        assert!(result.is_ok());
        assert_eq!(result.unwrap(), "fallback_result");
    }

    #[test]
    fn test_hatch_type_alias_usage() {
        fn test_function() -> AutoCorrectionResult<String> {
            Ok("success".to_string())
        }

        let result = test_function();
        assert!(result.is_ok());
        assert_eq!(result.unwrap(), "success");
    }

    #[test]
    fn test_utils_create_error_report() {
        let yoshi_err = factory::resource_exhausted_error("memory", 1024, 2048);
        let report = utils::create_error_report(&yoshi_err);

        assert!(report.contains("Error Report:"));
        assert!(report.contains("Category: Resource Management"));
        assert!(report.contains("Severity: Critical"));
        assert!(report.contains("Recovery Suggestions:"));
    }
}

// Remove duplicate module declaration
// pub mod recovery is already inline above
