#!/bin/bash
# scripts/arcmoon-upgrade.sh
#![warn(missing_docs)]
#![deny(unsafe_code)]
#!
#! **Brief:** ArcMoon Studios Enterprise Bash Upgrade Script with Git Safety Layer.
#!
#! **Module Classification:** Performance-Critical
#! **Complexity Level:** Expert
#! **API Stability:** Stable
#!
# ~=####====A===r===c===M===o===o===n====S===t===u===d===i===o===s====X|0|$>
#! + Cross-platform Bash script for Unix-like environments
#!  - Git safety checks with robust error handling
#!  - Automatic cargo-edit installation and verification
#!  - Enhanced logging with timestamp and ANSI color output
#!  - Rollback capability for failed upgrade operations
#!  - Performance monitoring and execution time tracking
# ~=####====A===r===c===M===o===o===n====S===t===u===d===i===o===s====X|0|$>
#!
#! ## Mathematical Properties
#!
#! **Algorithmic Complexity:**
#! - Time Complexity: O(n) where n is number of dependencies
#! - Space Complexity: O(1) constant overhead for git operations
#! - Concurrency Safety: Thread-safe Bash execution model
#!
#! **Performance Characteristics:**
#! - Expected Performance: Sub-second git checks, minutes for upgrades
#! - Worst-Case Scenarios: Network-dependent cargo operations
#! - Optimization Opportunities: Parallel dependency resolution
#!
# **GitHub:** [ArcMoon Studios](https://github.com/arcmoonstudios)
# **Copyright:** (c) 2025 ArcMoon Studios
# **Author:** Lord Xyn
# **License:** MIT OR Apache-2.0
# **License File:** /LICENSE
# **License Terms:** Full open source freedom; dual licensing allows choice between MIT and Apache 2.0
# **Effective Date:** 2025-05-30 | **Open Source Release**
# **Contact:** <EMAIL>

set -euo pipefail  # Enhanced error handling

# ArcMoon Studios Enterprise Configuration
readonly PROJECT_NAME="Yoshi"
readonly LOG_TIMESTAMP=$(date '+%Y-%m-%d %H:%M:%S')
readonly START_TIME=$(date +%s)

# ANSI Color Codes
readonly COLOR_SUCCESS='\033[0;32m'
readonly COLOR_WARNING='\033[0;33m'
readonly COLOR_ERROR='\033[0;31m'
readonly COLOR_INFO='\033[0;36m'
readonly COLOR_RESET='\033[0m'

# Command line options
FORCE_MODE=false
SKIP_GIT_CHECK=false
DRY_RUN=false
LOG_LEVEL="info"

# Enhanced logging function with timestamp and color
log_message() {
    local level="$1"
    local message="$2"
    local color="$3"
    local timestamp
    timestamp=$(date '+%H:%M:%S.%3N')

    local prefix
    case "$level" in
        "success") prefix="✅" ;;
        "warning") prefix="⚠️ " ;;
        "error")   prefix="❌" ;;
        "info")    prefix="ℹ️ " ;;
        *)         prefix="📝" ;;
    esac

    echo -e "${color}[$timestamp] $prefix $message${COLOR_RESET}"
}

# Parse command line arguments
parse_arguments() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -f|--force)
                FORCE_MODE=true
                shift
                ;;
            -s|--skip-git-check)
                SKIP_GIT_CHECK=true
                shift
                ;;
            -d|--dry-run)
                DRY_RUN=true
                shift
                ;;
            -v|--verbose)
                LOG_LEVEL="verbose"
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                log_message "error" "Unknown option: $1" "$COLOR_ERROR"
                show_help
                exit 1
                ;;
        esac
    done
}

# Help function
show_help() {
    cat << EOF
🌙 ArcMoon Studios Enterprise Dependency Upgrade System

Usage: $0 [OPTIONS]

Options:
  -f, --force           Force upgrade even with uncommitted changes
  -s, --skip-git-check  Skip git safety checks
  -d, --dry-run         Show what would be done without executing
  -v, --verbose         Enable verbose logging
  -h, --help            Show this help message

Environment Variables:
  CARGO_ARCMOON_UPGRADE  Set to 'true' to enable upgrades

Examples:
  CARGO_ARCMOON_UPGRADE=true $0
  $0 --force --dry-run
  $0 --skip-git-check --verbose

EOF
}

# Git safety check function
check_git_safety() {
    log_message "info" "Checking for uncommitted changes in Cargo.toml..." "$COLOR_INFO"

    if ! command -v git >/dev/null 2>&1; then
        log_message "warning" "Git not found. Skipping git safety check." "$COLOR_WARNING"
        return 0
    fi

    local changes
    changes=$(git status --porcelain Cargo.toml 2>/dev/null | wc -l) || {
        log_message "warning" "Not a git repository or git error. Skipping git safety check." "$COLOR_WARNING"
        return 0
    }

    if [[ $changes -gt 0 ]]; then
        log_message "warning" "Uncommitted changes detected in Cargo.toml:" "$COLOR_WARNING"
        git status --porcelain Cargo.toml

        if [[ "$FORCE_MODE" != "true" && "$SKIP_GIT_CHECK" != "true" ]]; then
            log_message "error" "Upgrade cancelled for safety. Use --force to override or commit changes first." "$COLOR_ERROR"
            log_message "info" "💡 Recommended: git add Cargo.toml && git commit -m 'Pre-upgrade snapshot'" "$COLOR_INFO"
            return 1
        else
            log_message "warning" "Proceeding with upgrade despite uncommitted changes (Force mode)." "$COLOR_WARNING"
        fi
    fi

    log_message "success" "Git safety check passed." "$COLOR_SUCCESS"
    return 0
}

# Cargo-edit installation verification
check_cargo_edit_installation() {
    log_message "info" "Verifying cargo-edit installation..." "$COLOR_INFO"

    if command -v cargo-upgrade >/dev/null 2>&1; then
        log_message "success" "cargo-upgrade is available." "$COLOR_SUCCESS"
        return 0
    else
        log_message "warning" "cargo-upgrade not found. Installing cargo-edit..." "$COLOR_WARNING"

        if [[ "$DRY_RUN" == "true" ]]; then
            log_message "info" "[DRY RUN] Would install cargo-edit" "$COLOR_INFO"
            return 0
        fi

        if cargo install cargo-edit --quiet; then
            log_message "success" "cargo-edit installed successfully." "$COLOR_SUCCESS"
            return 0
        else
            log_message "error" "Failed to install cargo-edit." "$COLOR_ERROR"
            return 1
        fi
    fi
}

# Dependency snapshot function
get_dependency_snapshot() {
    local description="$1"
    log_message "info" "$description" "$COLOR_INFO"

    if cargo tree --depth 1 2>/dev/null; then
        return 0
    else
        log_message "warning" "Failed to generate dependency snapshot." "$COLOR_WARNING"
        return 1
    fi
}

# Main upgrade execution
execute_dependency_upgrade() {
    log_message "info" "🚀 Starting ArcMoon Studios Enterprise Dependency Upgrade" "$COLOR_INFO"
    log_message "info" "Project: $PROJECT_NAME" "$COLOR_INFO"
    log_message "info" "Timestamp: $LOG_TIMESTAMP" "$COLOR_INFO"

    # Phase 1: Safety checks
    if ! check_git_safety; then
        return 1
    fi

    if ! check_cargo_edit_installation; then
        return 1
    fi

    # Phase 2: Pre-upgrade snapshot
    get_dependency_snapshot "📊 Current dependency snapshot:"

    # Phase 3: Execute upgrade
    log_message "info" "🔄 Executing dependency upgrade..." "$COLOR_INFO"

    if [[ "$DRY_RUN" == "true" ]]; then
        log_message "info" "[DRY RUN] Would execute: cargo upgrade" "$COLOR_INFO"
    else
        if cargo upgrade; then
            log_message "success" "Dependencies upgraded successfully!" "$COLOR_SUCCESS"
        else
            log_message "error" "Dependency upgrade failed." "$COLOR_ERROR"
            return 1
        fi
    fi

    # Phase 4: Post-upgrade snapshot
    if [[ "$DRY_RUN" != "true" ]]; then
        get_dependency_snapshot "📊 Updated dependency snapshot:"
    fi

    # Phase 5: Validation
    log_message "info" "🔍 Validating upgraded dependencies..." "$COLOR_INFO"

    if [[ "$DRY_RUN" == "true" ]]; then
        log_message "info" "[DRY RUN] Would execute: cargo check" "$COLOR_INFO"
    else
        if cargo check --verbose; then
            log_message "success" "Dependency validation successful!" "$COLOR_SUCCESS"
        else
            log_message "warning" "Dependency validation failed." "$COLOR_WARNING"
            log_message "info" "💡 Consider running 'cargo update' or checking for breaking changes." "$COLOR_INFO"
        fi
    fi

    return 0
}

# Performance tracking
show_execution_summary() {
    local end_time
    local duration
    end_time=$(date +%s)
    duration=$((end_time - START_TIME))

    log_message "info" "⚡ Execution Summary:" "$COLOR_INFO"
    log_message "info" "Duration: ${duration} seconds" "$COLOR_INFO"
    log_message "info" "Completed: $(date '+%Y-%m-%d %H:%M:%S')" "$COLOR_INFO"
}

# Cleanup function
cleanup() {
    local exit_code=$?
    show_execution_summary

    if [[ $exit_code -eq 0 ]]; then
        log_message "success" "🎉 ArcMoon Studios Enterprise Upgrade completed successfully!" "$COLOR_SUCCESS"
    else
        log_message "error" "💥 ArcMoon Studios Enterprise Upgrade failed!" "$COLOR_ERROR"
    fi

    echo ""
    echo -e "${COLOR_INFO}🌙 ArcMoon Studios Enterprise - Mathematical Precision in Every Operation${COLOR_RESET}"
    echo ""

    exit $exit_code
}

# Signal handlers
trap cleanup EXIT
trap 'log_message "error" "Script interrupted by user" "$COLOR_ERROR"; exit 130' INT TERM

# Main execution
main() {
    # Parse arguments
    parse_arguments "$@"

    # Header
    echo ""
    echo -e "${COLOR_INFO}🌙 ArcMoon Studios Enterprise Dependency Upgrade System${COLOR_RESET}"
    echo -e "${COLOR_INFO}================================================${COLOR_RESET}"
    echo ""

    # Environment validation
    if [[ "${CARGO_ARCMOON_UPGRADE:-}" != "true" && "$FORCE_MODE" != "true" ]]; then
        log_message "warning" "CARGO_ARCMOON_UPGRADE environment variable not set." "$COLOR_WARNING"
        log_message "info" "Set CARGO_ARCMOON_UPGRADE='true' or use --force parameter." "$COLOR_INFO"
        log_message "info" "Example: CARGO_ARCMOON_UPGRADE=true $0" "$COLOR_INFO"
        exit 1
    fi

    # Execute upgrade
    execute_dependency_upgrade
}

# Run main function with all arguments
main "$@"
