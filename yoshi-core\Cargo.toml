[package]
name = "yoshi-core"
version = "0.1.6"
edition = "2021"
rust-version = "1.75"
description = "Core error handling types and traits for the Yoshi ecosystem (no_std compatible)"
authors = ["<PERSON> <Lord<PERSON><PERSON>@proton.me>"]
license = "MIT OR Apache-2.0"
repository = "https://github.com/arcmoonstudios/yoshi-core"
documentation = "https://docs.rs/yoshi-core"
homepage = "https://github.com/arcmoonstudios/yoshi-core"
keywords = ["error", "error-handling", "no_std", "embedded", "diagnostics"]
categories = ["no-std", "embedded", "development-tools::debugging"]
readme = "README.md"
exclude = [".github/*", "examples/*", "benches/*", "tests/*"]

[features]
default = ["alloc"]

# Core features
alloc = []
std = ["alloc"]

# Serialization support
serde = ["dep:serde", "alloc"]

# Development and testing features
testing = []

[dependencies]
# Serialization (optional)
serde = { version = "1.0.219", features = [
    "derive",
    "alloc",
], optional = true, default-features = false }

[dev-dependencies]
# For testing serde functionality
serde_json = "1.0"

[package.metadata.docs.rs]
all-features = true
rustdoc-args = ["--cfg", "docsrs"]

# Examples will be added in future versions
# [[example]]
# name = "basic_usage"
# required-features = ["alloc"]
