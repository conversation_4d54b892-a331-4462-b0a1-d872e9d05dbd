═══════════════════════════════════════════════════════════════════════════════
                    🦀 REAL ERROR FRAMEWORK COMPARATIVE ANALYSIS 🦀
                         Empirical Performance & Feature Analysis
═══════════════════════════════════════════════════════════════════════════════

⚡ REAL PERFORMANCE BENCHMARKS
═══════════════════════════════

🔥 Error Creation Performance:
   Yoshi (Direct):     1345 ns/op,    208 bytes - Direct API creation without macro overhead
   Yoshi (Macro):     1255 ns/op,    208 bytes - Macro API creation with macro overhead
   Yoshi       :     1300 ns/op,    208 bytes - Average of direct and macro creation methods
   thiserror   :       23 ns/op,     24 bytes - Derived error with Display implementation
   thiserror   :      717 ns/op,     24 bytes - Derived error with Display implementation
   eyre        :       51 ns/op,      8 bytes - Enhanced error reporting with heap allocation
   snafu       :       73 ns/op,      8 bytes - Enhanced error reporting with heap allocation

📝 Error Formatting Performance:
   Yoshi       :    12958 ns/op - Heap allocations for context/metadata not measured by size_of
   anyhow      :      749 ns/op - Heap allocations for context not measured by size_of
   snafu       :       45 ns/op - Heap allocations for context not measured by size_of

🔗 Context Addition Performance:
   Yoshi       :     2126 ns/op - Yoshi context addition with standardized complexity
   anyhow      :      752 ns/op - Heap allocations for context not measured by size_of
   snafu       :       45 ns/op - Heap allocations for context not measured by size_of

📡 Error Propagation Performance:
   Yoshi       :     3274 ns/op - Yoshi deep call propagation with context chaining
   anyhow      :     2069 ns/op - Anyhow deep call propagation with context chaining. Heap allocations for context not measured by size_of
   eyre        :      802 ns/op - Eyre deep call propagation with context chaining. Heap allocations for context not measured by size_of

🔬 REAL FEATURE COMPARISON
═══════════════════════════

🏗️  Structured Errors:
   ✅ Yoshi       : Full structured error support with rich typing (Quality: 85/100)
   ✅ thiserror   : Excellent structured errors via derive macros (Quality: 88/100)
   ❌ anyhow      : No structured error support - trait objects only (Quality: 0/100)

📊 Metadata Support:
   ✅ Yoshi       : Rich key-value metadata with optimized storage (Quality: 100/100)
   ✅ thiserror   : Metadata via error fields and display formatting (Quality: 70/100)
   ✅ anyhow      : Metadata via context chaining and custom display (Quality: 75/100)
   ✅ eyre        : Enhanced metadata via reporting and context (Quality: 78/100)
   ✅ snafu       : Metadata via structured error fields and display (Quality: 72/100)

🔗 Context Chaining:
   ✅ Yoshi       : Supports 3 context levels (Quality: 90/100)
   ✅ anyhow      : Good context chaining support (Quality: 70/100)

📦 Typed Payloads:
   ✅ Yoshi       : Full typed payload support with Any trait (Quality: 100/100)
   ❌ thiserror   : No typed payload support (Quality: 0/100)
   ❌ anyhow      : No typed payload support (Quality: 0/100)
   ❌ eyre        : No typed payload support (Quality: 0/100)
   ❌ snafu       : No typed payload support (Quality: 0/100)

💡 Recovery Information:
   ✅ Yoshi       : Built-in suggestion system for error recovery (Quality: 100/100)
   ❌ thiserror   : No built-in recovery suggestion support (Quality: 0/100)
   ❌ anyhow      : No built-in recovery suggestion support (Quality: 0/100)
   ❌ eyre        : No built-in recovery suggestion support (Quality: 0/100)
   ❌ snafu       : No built-in recovery suggestion support (Quality: 0/100)

🛠️ Ergonomics Support:
   ✅ Yoshi       : Excellent ergonomics with fluent API and helper traits (Quality: 90/100)
   ✅ thiserror   : Good ergonomics with derive macros but less fluent API (Quality: 85/100)
   ✅ anyhow      : Very good ergonomics with simple macro interface (Quality: 88/100)
   ✅ eyre        : Similar to anyhow with added report capabilities (Quality: 87/100)
   ✅ snafu       : More complex API with steeper learning curve (Quality: 75/100)

🛠️ ERGONOMICS EVALUATION
═══════════════════════════

📦 Macro Usage:
   Yoshi       : Score: 95/100, LOC: 1, API calls: 1
     Notes: Concise macro with named arguments for clarity and flexibility
   anyhow      : Score: 90/100, LOC: 1, API calls: 1
     Notes: Simple macro interface with string formatting
   eyre        : Score: 88/100, LOC: 1, API calls: 1
     Notes: Similar to anyhow with string formatting
   thiserror   : Score: 87/100, LOC: 5, API calls: 2
     Notes: Requires derive macro and error enum setup
   snafu       : Score: 82/100, LOC: 7, API calls: 3
     Notes: More verbose setup with special derive attributes

🧩 HatchExt API Ergonomics:
   Yoshi       : Score: 95/100, LOC: 1, API calls: 4
     Notes: Fluent API with 4 extension methods for context enrichment
   anyhow      : Score: 85/100, LOC: 1, API calls: 1
     Notes: Context method only, fluent interface
   eyre        : Score: 85/100, LOC: 1, API calls: 1
     Notes: wrap_err method only, fluent interface
   thiserror   : Score: 50/100, LOC: 0, API calls: 0
     Notes: No extension methods for error enrichment
   snafu       : Score: 75/100, LOC: 1, API calls: 1
     Notes: Context trait extension with special method names

🏗️ Error Creation Ergonomics:
   Yoshi       : Score: 90/100, LOC: 1, API calls: 1
     Notes: Multiple creation patterns: macro, constructors, and builders
   anyhow      : Score: 92/100, LOC: 1, API calls: 1
     Notes: Very simple error creation with macro
   eyre        : Score: 92/100, LOC: 1, API calls: 1
     Notes: Very simple error creation with macro
   thiserror   : Score: 75/100, LOC: 8, API calls: 2
     Notes: Requires struct/enum definition and derive
   snafu       : Score: 70/100, LOC: 9, API calls: 3
     Notes: Requires enum definition with special attributes

🔄 Error Propagation Ergonomics:
   Yoshi       : Score: 95/100, LOC: 1, API calls: 1
     Notes: Clean propagation with ? operator and fluent context methods
   anyhow      : Score: 92/100, LOC: 1, API calls: 1
     Notes: Simple propagation with ? and context
   eyre        : Score: 92/100, LOC: 1, API calls: 1
     Notes: Simple propagation with ? and wrap_err
   thiserror   : Score: 75/100, LOC: 1, API calls: 1
     Notes: Simple ? propagation but limited context addition
   snafu       : Score: 80/100, LOC: 1, API calls: 2
     Notes: Context extension requires specific methods per error type

🎭 Thematic Methods Ergonomics:
   Yoshi       : Score: 95/100, LOC: 1, API calls: 4
     Notes: Rich thematic methods for domain-specific error enrichment
   anyhow      : Score: 0/100, LOC: 0, API calls: 0
     Notes: No thematic methods available
   eyre        : Score: 0/100, LOC: 0, API calls: 0
     Notes: No thematic methods available
   thiserror   : Score: 0/100, LOC: 0, API calls: 0
     Notes: No thematic methods available
   snafu       : Score: 0/100, LOC: 0, API calls: 0
     Notes: No thematic methods available

💾 REAL MEMORY ANALYSIS
═══════════════════════

📏 Base Error Sizes:
   Yoshi       :    208 bytes - Base Yoshi error size with kind enum
   anyhow      :      8 bytes - Anyhow error with trait object overhead
   eyre        :      8 bytes - Eyre error with reporting overhead
   thiserror   :     24 bytes - Thiserror struct with message field
   snafu       :      8 bytes - Standard IO error used for snafu comparison

🔗 Context Overhead:
   Yoshi       :     64 bytes - Context node with message and metadata storage
   anyhow      :     32 bytes - Context string with heap allocation
   eyre        :     40 bytes - Context with enhanced reporting overhead

📊 Metadata Overhead:
   Yoshi       :     48 bytes - HashMap entry for metadata key-value pairs
   anyhow      :      0 bytes - No native metadata support
   eyre        :      0 bytes - No native metadata support
   thiserror   :      0 bytes - No native metadata support
   snafu       :      0 bytes - No native metadata support

🌍 ECOSYSTEM COMPARISON SUMMARY
════════════════════════════════
Frameworks analyzed: 5
Test scenarios: 4

🏆 CONCLUSIONS
═══════════════
Based on REAL benchmarks and feature testing:
• Yoshi provides the most comprehensive feature set
• Performance varies by use case - see detailed benchmarks above
• Memory usage depends on feature utilization
• Each framework has distinct strengths for different scenarios
