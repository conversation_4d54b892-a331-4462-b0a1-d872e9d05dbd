# yoshi-derive/Cargo.toml
[package]
name = "yoshi-derive"
version = "0.1.6"
edition = "2021"
rust-version = "1.75.0"
authors = ["<PERSON>yn <<PERSON><PERSON><PERSON>@proton.me>"]
repository = "https://github.com/arcmoonstudios/yoshi"
license = "MIT OR Apache-2.0"
description = "Sophisticated procedural macro for deriving Yoshi error types with intelligent auto-inference and LSP integration."
keywords = ["proc-macro", "error", "derive", "error-handling", "yoshi"]
categories = ["development-tools", "rust-patterns"]
readme = "README.md"

[lib]
proc-macro = true

[dependencies]
# Core proc-macro dependencies
darling = "0.20.11"
dashmap = "6.1.0"
proc-macro2 = "1.0.95"
once_cell = "1.21.3"
quote = "1.0.40"

# Additional proc-macro dependencies
syn = { version = "2.0.101", features = ["full", "extra-traits", "derive"] }

# Integration with yoshi-std for conversion implementations
yoshi-std = { version = "0.1.6", path = "../yoshi-std" }

# Optional dependencies for testing features
trybuild = { version = "1.0.105", optional = true }
serde = { version = "1.0.219", features = ["derive"], optional = true }

[dev-dependencies]
# Testing dependencies (always available in tests)
serde_json = "1.0.140"

# Additional testing utilities
pretty_assertions = "1.4.1"
paste = "1.0.15"

[features]
default = ["std"]

# Standard library support (enabled by default)
std = []

# Enable performance optimizations for large enums
optimize-large = []

# Enable serde support for testing
serde = ["dep:serde"]

# Enable compile-fail testing
compile-fail-tests = ["trybuild"]

# Enable full testing (just a marker feature, doesn't enable yoshi-std since it's always available)
full-testing = []

# docs.rs configuration
[package.metadata.docs.rs]
rustc-args = ["--cap-lints=warn"]
rustdoc-args = ["--cfg", "docsrs"]
targets = ["x86_64-unknown-linux-gnu"]

# Test configurations
[[test]]
name = "diagnostic_test"
path = "tests/diagnostic_test.rs"

[[test]]
name = "integration_tests"
path = "tests/integration_tests.rs"

[[test]]
name = "compilation_tests"
path = "tests/compilation_tests.rs"

[[test]]
name = "error_handling_tests"
path = "tests/error_handling_tests.rs"

[[test]]
name = "test_runner"
path = "tests/test_runner.rs"

[[test]]
name = "derive_test"
path = "tests/derive_test.rs"

# Compile-fail tests (when enabled)
[[test]]
name = "compile_fail_tests"
path = "tests/compile_fail_tests.rs"
required-features = ["compile-fail-tests"]
