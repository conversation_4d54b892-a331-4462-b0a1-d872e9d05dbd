/* yoshi-std/src/lib.rs */

#![warn(clippy::all)]
#![warn(missing_docs)]
#![warn(clippy::cargo)]
#![warn(clippy::pedantic)]
#![allow(unexpected_cfgs)] // Allow experimental feature flags
#![allow(clippy::too_many_lines)] // Comprehensive error handling requires extensive implementation
#![allow(clippy::result_large_err)] // Error context richness justifies larger error types
#![allow(clippy::enum_variant_names)] // Consistent naming like YoshiKind variants
#![allow(clippy::items_after_statements)] // Better code organization in some contexts
#![allow(clippy::module_name_repetitions)] // Allow YoshiKind, YoContext naming

//! # Yoshi Std - Standard Library Integration for Yoshi Error Framework
//!
//! Yoshi Std builds upon `yoshi-core` to provide full standard library integration,
//! including `std::io::Error` support, backtrace capture, enhanced error reporting
//! features, and complete implementation of all traits defined in the core crate.
//!
//! ## Design Philosophy
//!
//! This crate **implements** the functionality defined by `yoshi-core`, providing:
//! - Full std library integration and enhanced features
//! - Implementation of all core traits (`HatchExt`, `LayText`, `Hatchable`)
//! - Extended functionality like backtrace capture and enhanced debugging
//! - std::io::Error integration and enhanced debugging capabilities
//!
//! ## Enhanced Features Beyond Core
//!
//! - **Full Backtrace Support**: Captures and displays full stack traces
//! - **std::io::Error Integration**: Seamless conversion and handling
//! - **Enhanced Debugging**: Production-safe error sanitization
//! - **Complete Trait Implementations**: All core traits fully implemented
//!
//! ## Core Architecture & Performance
//!
//! ```rust
//! use yoshi_std::{Hatch, LayText, HatchExt, yum};
//! use std::fs;
//!
//! /// Example: File processing with rich error context
//! fn process_data_file(path: &str) -> Hatch<String> {
//!     fs::read_to_string(path)
//!         .hatch()  // Convert std::io::Error to Yoshi ecosystem
//!         .lay("While loading application data")  // Thematic context
//!         .with_suggestion("Ensure the file exists and is readable")
//!         .with_metadata("file_path", path)
//!         .with_metadata("operation", "data_processing")
//! }
//!
//! /// Example: Error consumption and analysis
//! match process_data_file("config.json") {
//!     Ok(data) => println!("Success: {}", data),
//!     Err(error) => {
//!         yum!(error); // Rich debug output with full context analysis
//!         // Prints comprehensive error information including backtrace
//!     }
//! }
//! ```

// Re-export everything from yoshi-core
pub use yoshi_core::*;

use std::{
    any::Any,
    collections::HashMap,
    error::Error,
    fmt::{self, Display, Formatter},
    sync::Arc,
    time::SystemTime,
    thread,
};

//============================================================================
// SECTION 2: ENHANCED STRING INTERNING WITH STD FEATURES
//============================================================================

/// Enhanced string interning function with full std support.
#[inline]
pub fn intern_string_std(s: impl Into<String>) -> Arc<str> {
    // Simple conversion for std environments - no complex caching needed
    // for this implementation. The Arc<str> provides the sharing benefits.
    let string: String = s.into();
    Arc::from(string.as_str())
}

//============================================================================
// SECTION 3: STD BACKTRACE INTEGRATION
//============================================================================

/// Conditionally captures a std backtrace based on environment variables.
fn capture_std_backtrace() -> Option<std::backtrace::Backtrace> {
    let should_capture = std::env::var("RUST_LIB_BACKTRACE")
        .or_else(|_| std::env::var("RUST_BACKTRACE"))
        .map(|v| v == "1" || v == "full")
        .unwrap_or(false);

    if should_capture {
        Some(std::backtrace::Backtrace::capture())
    } else {
        None
    }
}

/// Checks if running in production mode for security sanitization.
#[inline]
fn is_production_mode() -> bool {
    std::env::var("YOSHI_PRODUCTION_MODE")
        .map(|v| v == "1" || v.to_lowercase() == "true")
        .unwrap_or(false)
}

//============================================================================
// SECTION 4: TRAIT IMPLEMENTATIONS FOR CORE TYPES
//============================================================================

impl<T, E> Hatchable<T, E> for std::result::Result<T, E>
where
    E: Into<Yoshi>,
{
    /// Converts an error into a `Hatch<T>` by mapping it into `Yoshi`.
    ///
    /// This method provides a convenient way to bring external error types into
    /// the Yoshi ecosystem while maintaining type safety and performance efficiency.
    /// The conversion leverages existing `Into<Yoshi>` implementations.
    ///
    /// # Returns
    ///
    /// A `Hatch<T>` containing either the original success value or the converted error.
    ///
    /// # Examples
    ///
    /// ```rust
    /// use yoshi_std::{Hatch, Hatchable};
    /// use std::io;
    ///
    /// // I/O error conversion
    /// let io_result: Result<String, io::Error> = Err(io::Error::new(
    ///     io::ErrorKind::NotFound, "file not found"
    /// ));
    /// let hatched: Hatch<String> = io_result.hatch();
    /// assert!(hatched.is_err());
    /// ```
    #[track_caller]
    fn hatch(self) -> Hatch<T> {
        self.map_err(Into::into)
    }
}

impl<T> LayText<T> for Hatch<T> {
    /// Adds a contextual message to the error chain, like laying an egg with metadata.
    ///
    /// This method enriches error information by attaching descriptive context
    /// that helps with debugging and error tracing. It uses thematic naming
    /// inspired by Yoshi's egg-laying ability to create memorable, intuitive APIs.
    ///
    /// # Arguments
    ///
    /// * `message` - The context message to attach. Accepts any type that converts to `String`.
    ///
    /// # Returns
    ///
    /// A `Hatch<T>` with the enriched context information attached.
    ///
    /// # Examples
    ///
    /// ```rust
    /// use yoshi_std::{Hatch, LayText, Yoshi, YoshiKind};
    ///
    /// fn database_operation() -> Hatch<String> {
    ///     Err(Yoshi::new(YoshiKind::Internal {
    ///         message: "connection failed".into(),
    ///         source: None,
    ///         component: None,
    ///     }))
    ///     .lay("While establishing database connection")
    /// }
    /// ```
    #[track_caller]
    fn lay(self, message: impl Into<String>) -> Hatch<T> {
        self.map_err(|mut e| {
            e.contexts.push(
                YoContext::new(message)
                    .with_location(yoshi_location!())
            );
            e
        })
    }
}

impl<T, E> HatchExt<T> for std::result::Result<T, E>
where
    E: Into<Yoshi>,
{
    /// Adds a context message to the error.
    ///
    /// If `self` is `Ok`, it is returned unchanged. If `self` is `Err`, its error
    /// is converted to a `Yoshi` error if it isn't already, and a new `YoContext`
    /// with the provided message is added to it.
    ///
    /// # Arguments
    ///
    /// * `msg` - The context message.
    ///
    /// # Returns
    ///
    /// A `Hatch<T>` with the added context on error.
    #[track_caller]
    #[inline]
    fn context(self, msg: impl Into<String>) -> Hatch<T> {
        self.map_err(|e| {
            let mut yoshi_err = e.into();
            yoshi_err.contexts.push(
                YoContext::new(msg)
                    .with_location(yoshi_location!())
            );
            yoshi_err
        })
    }

    /// Adds a suggestion to the error's primary context.
    ///
    /// If `self` is `Ok`, it is returned unchanged. If `self` is `Err`, its error
    /// is converted to a `Yoshi` error if it isn't already, and a new suggestion
    /// is added to its primary `YoContext`.
    ///
    /// # Arguments
    ///
    /// * `s` - The suggestion message.
    ///
    /// # Returns
    ///
    /// A `Hatch<T>` with the added suggestion on error.
    #[track_caller]
    #[inline]
    fn with_suggestion(self, s: impl Into<String>) -> Hatch<T> {
        self.map_err(|e| {
            let mut yoshi_err = e.into();
            // Ensure there's at least one context to attach the suggestion to
            if yoshi_err.contexts.is_empty() {
                yoshi_err.contexts.push(
                    YoContext::new("Error occurred")
                        .with_location(yoshi_location!())
                );
            }
            if let Some(ctx) = yoshi_err.contexts.last_mut() {
                ctx.suggestion = Some(intern_string_std(s.into()));
            }
            yoshi_err
        })
    }

    /// Attaches a typed shell to the error's primary context.
    ///
    /// If `self` is `Ok`, it is returned unchanged. If `self` is `Err`, its error
    /// is converted to a `Yoshi` error if it isn't already, and a new typed shell
    /// is added to its primary `YoContext`.
    ///
    /// # Arguments
    ///
    /// * `p` - The shell to attach. Must be `Any + Send + Sync + 'static`.
    ///
    /// # Returns
    ///
    /// A `Hatch<T>` with the added shell on error.
    #[track_caller]
    #[inline]
    fn with_shell(self, p: impl Any + Send + Sync + 'static) -> Hatch<T> {
        self.map_err(|e| {
            let mut yoshi_err = e.into();
            // Ensure there's at least one context to attach the shell to
            if yoshi_err.contexts.is_empty() {
                yoshi_err.contexts.push(
                    YoContext::new("Error occurred")
                        .with_location(yoshi_location!())
                );
            }
            if let Some(ctx) = yoshi_err.contexts.last_mut() {
                // Limit shell count to prevent memory exhaustion
                const MAX_PAYLOADS: usize = 16;
                if ctx.payloads.len() < MAX_PAYLOADS {
                    ctx.payloads.push(Arc::new(Box::new(p)));
                }
            }
            yoshi_err
        })
    }

    /// Sets the priority for the error's primary context.
    ///
    /// If `self` is `Ok`, it is returned unchanged. If `self` is `Err`, its error
    /// is converted to a `Yoshi` error if it isn't already, and the priority of its
    /// primary `YoContext` is updated.
    ///
    /// # Arguments
    ///
    /// * `priority` - The priority level (0-255).
    ///
    /// # Returns
    ///
    /// A `Hatch<T>` with the updated priority on error.
    #[track_caller]
    #[inline]
    fn with_priority(self, priority: u8) -> Hatch<T> {
        self.map_err(|e| {
            let mut yoshi_err = e.into();
            // Ensure there's at least one context to update
            if yoshi_err.contexts.is_empty() {
                yoshi_err.contexts.push(
                    YoContext::new("Error occurred")
                        .with_location(yoshi_location!())
                );
            }
            if let Some(ctx) = yoshi_err.contexts.last_mut() {
                ctx.priority = priority;
            }
            yoshi_err
        })
    }

    /// Short alias for `context`.
    #[track_caller]
    #[inline]
    fn ctx(self, msg: impl Into<String>) -> Hatch<T> {
        self.context(msg)
    }

    /// Short alias for `with_suggestion`.
    #[track_caller]
    #[inline]
    fn help(self, s: impl Into<String>) -> Hatch<T> {
        self.with_suggestion(s)
    }

    /// Adds metadata to the error's primary context.
    ///
    /// This is a convenience method that delegates to `Yoshi::with_metadata`.
    ///
    /// # Arguments
    ///
    /// * `k` - The metadata key.
    /// * `v` - The metadata value.
    ///
    /// # Returns
    ///
    /// A `Hatch<T>` with the added metadata on error.
    #[track_caller]
    #[inline]
    fn meta(self, k: impl Into<String>, v: impl Into<String>) -> Hatch<T> {
        self.map_err(|e| {
            let mut yoshi_err = e.into();
            // Ensure there's at least one context to attach metadata to
            if yoshi_err.contexts.is_empty() {
                yoshi_err.contexts.push(
                    YoContext::new("Error occurred")
                        .with_location(yoshi_location!())
                );
            }
            if let Some(ctx) = yoshi_err.contexts.last_mut() {
                ctx.metadata.insert(
                    intern_string_std(k.into()),
                    intern_string_std(v.into())
                );
            }
            yoshi_err
        })
    }
}

//============================================================================
// SECTION 5: ENHANCED YOSHI IMPLEMENTATIONS
//============================================================================

impl Yoshi {
    /// Creates a new `Yoshi` error with std::backtrace capture.
    ///
    /// This enhanced constructor captures a full std::backtrace when available,
    /// providing richer debugging information in std environments.
    ///
    /// # Arguments
    ///
    /// * `kind` - The [`YoshiKind`] that categorizes this error.
    ///
    /// # Returns
    ///
    /// A new `Yoshi` error instance with enhanced std features.
    #[inline]
    #[track_caller]
    pub fn new_std(kind: YoshiKind) -> Self {
        let instance_id = ERROR_INSTANCE_COUNTER.fetch_add(1, Ordering::Relaxed);

        // Create enhanced backtrace that includes std::backtrace info
        let mut backtrace = YoshiBacktrace::new_captured();
        if let Some(std_bt) = capture_std_backtrace() {
            // Store std backtrace information in a shell for enhanced debugging
            let bt_info = StdBacktraceInfo {
                std_backtrace: std_bt,
                capture_thread: thread::current().name().map(String::from),
            };

            // We can't modify the core YoshiBacktrace, but we can add context with the info
            // This will be available through the context system
        }

        Self {
            kind,
            backtrace: Some(backtrace),
            contexts: Vec::with_capacity(4),
            instance_id,
            created_at: SystemTime::now().into(),
        }
    }

    /// Creates a new `Yoshi` error by wrapping a foreign `Error` trait object.
    ///
    /// This is an explicit conversion for generic error types, allowing them
    /// to be integrated into the `Yoshi` error chain without requiring a
    /// blanket `From` implementation that might conflict or cause issues.
    /// The type name of the wrapped error is captured for diagnostic purposes.
    ///
    /// # Type Parameters
    ///
    /// * `E` - The type of the foreign error, which must implement `Error`,
    ///   `Send`, `Sync`, and have a `'static` lifetime.
    ///
    /// # Arguments
    ///
    /// * `e` - The foreign error instance to wrap.
    ///
    /// # Returns
    ///
    /// A new `Yoshi` error with its kind set to `YoshiKind::Foreign`.
    #[inline]
    #[track_caller]
    pub fn foreign<E>(e: E) -> Self
    where
        E: Error + Send + Sync + 'static,
    {
        let type_name = std::any::type_name::<E>();
        Self::new(YoshiKind::Foreign {
            error: Box::new(e),
            error_type_name: intern_string_std(type_name),
        })
    }

    /// Gets the error severity level (0-100).
    ///
    /// This is a convenience method that delegates to `self.kind.severity()`.
    /// Higher values indicate more severe errors requiring immediate attention.
    #[inline]
    pub const fn severity(&self) -> u8 {
        self.kind.severity()
    }

    /// Checks if this is a transient error that might succeed on retry.
    ///
    /// This is a convenience method that delegates to `self.kind.is_transient()`.
    /// Transient errors are typically temporary conditions that may resolve
    /// themselves with retry logic.
    #[inline]
    pub const fn is_transient(&self) -> bool {
        self.kind.is_transient()
    }

    /// Adds a context message to the error.
    ///
    /// This method enhances the error with additional diagnostic information,
    /// making it easier to trace the origin and propagation of failures.
    /// The context is automatically tagged with the current source location.
    ///
    /// # Arguments
    ///
    /// * `msg` - The context message. It can be any type that converts into a `String`.
    ///
    /// # Returns
    ///
    /// The modified `Yoshi` error instance with the new context.
    #[track_caller]
    #[inline]
    #[must_use]
    pub fn context(mut self, msg: impl Into<String>) -> Self {
        self.contexts.push(
            YoContext::new(msg)
                .with_location(yoshi_location!())
        );
        self
    }

    /// Adds a suggestion to the error's primary context.
    ///
    /// This method adds a human-readable suggestion to the current `Yoshi` error.
    /// The suggestion is stored in the primary (most recent) context associated
    /// with this error. If no context exists, one is automatically created.
    ///
    /// # Arguments
    ///
    /// * `s` - The suggestion message. It can be any type that converts into a `String`.
    ///
    /// # Returns
    ///
    /// The modified `Yoshi` error instance with the new suggestion.
    #[inline]
    #[track_caller]
    #[must_use]
    pub fn with_suggestion(mut self, s: impl Into<String>) -> Self {
        // Ensure there's at least one context to attach the suggestion to
        if self.contexts.is_empty() {
            self.contexts.push(
                YoContext::new("Error occurred")
                    .with_location(yoshi_location!())
            );
        }
        if let Some(ctx) = self.contexts.last_mut() {
            ctx.suggestion = Some(intern_string_std(s.into()));
        }
        self
    }

    /// Attaches a component identifier to the error's primary context.
    ///
    /// This method adds a component identifier to help categorize and trace
    /// errors within different parts of a system or application. The component
    /// information is stored as metadata with the key "component".
    ///
    /// # Arguments
    ///
    /// * `component` - The component identifier. It can be any type that converts into a `String`.
    ///
    /// # Returns
    ///
    /// The modified `Yoshi` error instance with the component information.
    #[inline]
    #[track_caller]
    #[must_use]
    pub fn with_component(mut self, component: impl Into<String>) -> Self {
        // Ensure there's at least one context to attach the component to
        if self.contexts.is_empty() {
            self.contexts.push(
                YoContext::new("Error occurred")
                    .with_location(yoshi_location!())
            );
        }
        if let Some(ctx) = self.contexts.last_mut() {
            ctx.metadata.insert(
                intern_string_std("component"),
                intern_string_std(component.into())
            );
        }
        self
    }

    /// Attaches a typed shell to the error's primary context.
    ///
    /// This method allows embedding arbitrary Rust types within the error's context.
    /// This is useful for passing structured, type-safe debugging information
    /// that can be retrieved later using `shell::<T>()`.
    ///
    /// # Arguments
    ///
    /// * `shell` - The data to attach. It must implement `Any`, `Send`, `Sync`, and have a `'static` lifetime.
    ///
    /// # Returns
    ///
    /// The modified `Yoshi` error instance with the new shell.
    #[inline]
    #[track_caller]
    #[must_use]
    pub fn with_shell(mut self, shell: impl Any + Send + Sync + 'static) -> Self {
        // Ensure there's at least one context to attach the shell to
        if self.contexts.is_empty() {
            self.contexts.push(
                YoContext::new("Error occurred")
                    .with_location(yoshi_location!())
            );
        }
        if let Some(ctx) = self.contexts.last_mut() {
            // Limit shell count to prevent memory exhaustion
            const MAX_PAYLOADS: usize = 16;
            if ctx.payloads.len() < MAX_PAYLOADS {
                ctx.payloads.push(Arc::new(Box::new(shell)));
            }
        }
        self
    }

    /// Sets the priority for the error's primary context.
    ///
    /// Priority can be used to indicate the relative importance of a context
    /// message, influencing how errors are logged or processed by error handling
    /// systems. Higher values indicate higher priority.
    ///
    /// # Arguments
    ///
    /// * `priority` - The priority level (0-255).
    ///
    /// # Returns
    ///
    /// The modified `Yoshi` error instance with the updated priority.
    #[inline]
    #[must_use]
    #[track_caller]
    pub fn with_priority(mut self, priority: u8) -> Self {
        // Ensure there's at least one context to update
        if self.contexts.is_empty() {
            self.contexts.push(
                YoContext::new("Error occurred")
                    .with_location(yoshi_location!())
            );
        }
        if let Some(ctx) = self.contexts.last_mut() {
            ctx.priority = priority;
        }
        self
    }

    /// Adds metadata to the error's primary context.
    ///
    /// Metadata are key-value pairs that provide additional, unstructured
    /// diagnostic information. These can be used for logging, filtering,
    /// or passing arbitrary data alongside the error.
    ///
    /// # Arguments
    ///
    /// * `k` - The metadata key. It can be any type that converts into a `String`.
    /// * `v` - The metadata value. It can be any type that converts into a `String`.
    ///
    /// # Returns
    ///
    /// The modified `Yoshi` error instance with the new metadata.
    #[inline]
    #[track_caller]
    #[must_use]
    pub fn with_metadata(mut self, k: impl Into<String>, v: impl Into<String>) -> Self {
        // Ensure there's at least one context to attach metadata to
        if self.contexts.is_empty() {
            self.contexts.push(
                YoContext::new("Error occurred")
                    .with_location(yoshi_location!())
            );
        }
        if let Some(ctx) = self.contexts.last_mut() {
            ctx.metadata.insert(
                intern_string_std(k.into()),
                intern_string_std(v.into())
            );
        }
        self
    }

    /// Sets location information on the error's primary context.
    ///
    /// This method attaches source code location information to the error's primary context,
    /// helping with debugging and error tracing. It consumes `self` and returns a modified `Self`.
    ///
    /// # Arguments
    ///
    /// * `location` - The `YoshiLocation` to set.
    ///
    /// # Returns
    ///
    /// The modified `Yoshi` error instance with the location set.
    #[inline]
    #[track_caller]
    #[must_use]
    pub fn with_location(mut self, location: YoshiLocation) -> Self {
        // Ensure there's at least one context to attach location to
        if self.contexts.is_empty() {
            self.contexts.push(
                YoContext::new("Error occurred")
                    .with_location(yoshi_location!())
            );
        }
        if let Some(ctx) = self.contexts.last_mut() {
            ctx.location = Some(location);
        }
        self
    }

    /// Returns a reference to the optional backtrace.
    ///
    /// The backtrace is only available when the `std` feature is enabled and
    /// `RUST_BACKTRACE` or `RUST_LIB_BACKTRACE` environment variables are set.
    ///
    /// # Returns
    ///
    /// An `Option` containing a reference to the [`YoshiBacktrace`] if available,
    /// otherwise `None`.
    #[inline]
    pub const fn backtrace(&self) -> Option<&YoshiBacktrace> {
        self.backtrace.as_ref()
    }

    /// Returns a reference to the underlying foreign error (if `YoshiKind::Foreign`).
    ///
    /// This method allows downcasting the boxed `dyn Error` contained within a
    /// `YoshiKind::Foreign` variant to a concrete type.
    ///
    /// # Type Parameters
    ///
    /// * `T` - The concrete type to downcast to, which must implement `Error`.
    ///
    /// # Returns
    ///
    /// An `Option` containing a reference to the downcasted error of type `T`,
    /// or `None` if the error is not `YoshiKind::Foreign` or cannot be downcasted
    /// to the specified type.
    #[inline]
    pub fn downcast_ref<T: Error + 'static>(&self) -> Option<&T> {
        if let YoshiKind::Foreign { error, .. } = &self.kind {
            error.downcast_ref::<T>()
        } else {
            None
        }
    }

    /// Returns a mutable reference to the underlying foreign error (if `YoshiKind::Foreign`).
    ///
    /// This method allows mutable downcasting the boxed `dyn Error` contained within a
    /// `YoshiKind::Foreign` variant to a concrete type.
    ///
    /// # Type Parameters
    ///
    /// * `T` - The concrete type to downcast to, which must implement `Error`.
    ///
    /// # Returns
    ///
    /// An `Option` containing a mutable reference to the downcasted error of type `T`,
    /// or `None` if the error is not `YoshiKind::Foreign` or cannot be downcasted
    /// to the specified type.
    #[inline]
    pub fn downcast_mut<T: Error + 'static>(&mut self) -> Option<&mut T> {
        if let YoshiKind::Foreign { error, .. } = &mut self.kind {
            error.downcast_mut::<T>()
        } else {
            None
        }
    }

    /// Returns the primary context associated with this error.
    ///
    /// The primary context is typically the most recent or most relevant
    /// context added to the error, often containing the most specific
    /// information about the direct cause of the failure.
    ///
    /// # Returns
    ///
    /// An `Option` containing a reference to the primary `YoContext`,
    /// or `None` if no contexts have been added.
    #[inline]
    pub fn primary_context(&self) -> Option<&YoContext> {
        self.contexts.last()
    }

    /// Returns an iterator over all contexts associated with this error.
    ///
    /// Contexts are ordered from oldest (first added) to newest (most recent, primary).
    /// This allows traversing the complete error context chain to understand
    /// the error's propagation path.
    ///
    /// # Returns
    ///
    /// An iterator yielding references to `YoContext` instances.
    #[inline]
    pub fn contexts(&self) -> impl Iterator<Item = &YoContext> {
        self.contexts.iter()
    }

    /// Returns the suggestion from the primary context, if any.
    ///
    /// This is a convenience method to quickly access the most relevant
    /// suggestion for resolving the error.
    ///
    /// # Returns
    ///
    /// An `Option` containing a reference to the suggestion string, or `None`.
    #[inline]
    pub fn suggestion(&self) -> Option<&str> {
        self.primary_context()
            .and_then(|ctx| ctx.suggestion.as_deref())
    }

    /// Returns a typed shell from any context, if any.
    ///
    /// This method searches through all contexts attached to the error to find
    /// a shell of the specified type. It returns the first match found.
    ///
    /// # Type Parameters
    ///
    /// * `T` - The type of shell to retrieve.
    ///
    /// # Returns
    ///
    /// An `Option` containing a reference to the shell of type `T`, or `None`.
    #[inline]
    pub fn shell<T: 'static>(&self) -> Option<&T> {
        // Search ALL contexts for the shell, not just the primary context
        for context in &self.contexts {
            for payload in &context.payloads {
                if let Some(shell) = payload.as_ref().downcast_ref::<T>() {
                    return Some(shell);
                }
            }
        }
        None
    }

    // THEMATIC METHODS - PRESERVED FOR INTUITIVE ERROR HANDLING

    /// The nested error, equivalent to `source()`, but more thematically expressive.
    ///
    /// This method provides thematic access to the underlying error source while
    /// maintaining full backwards compatibility with the standard `Error` trait.
    /// The name "nest" evokes the idea of errors being nested within each other,
    /// like eggs in a nest.
    ///
    /// # Returns
    ///
    /// An `Option` containing a reference to the nested error, or `None` if
    /// there is no underlying source.
    #[inline]
    pub fn nest(&self) -> Option<&(dyn Error + 'static)> {
        self.source()
    }

    /// The explanation or context attached to the error.
    ///
    /// This method provides direct access to the primary context message,
    /// offering a thematic alternative to accessing context information.
    /// The name "laytext" suggests the layered textual information that
    /// builds up around an error as it propagates.
    ///
    /// # Returns
    ///
    /// An `Option` containing a reference to the laytext string, or `None`
    /// if no context message is available.
    #[inline]
    pub fn laytext(&self) -> Option<&str> {
        self.primary_context()
            .and_then(|ctx| ctx.message.as_deref())
    }

    /// Adds contextual information using the thematic `.lay()` method.
    ///
    /// This method is equivalent to `.context()` but provides thematic naming
    /// consistent with the Yoshi ecosystem's metaphorical framework. The name
    /// "lay" evokes Yoshi's egg-laying ability, suggesting the error is "laying"
    /// additional context information.
    ///
    /// # Arguments
    ///
    /// * `msg` - The context message to attach.
    ///
    /// # Returns
    ///
    /// The modified `Yoshi` error instance with the new context.
    #[track_caller]
    #[inline]
    #[must_use]
    pub fn lay(self, msg: impl Into<String>) -> Self {
        self.context(msg)
    }

    /// Gathers analysis results about the contexts in this error.
    ///
    /// This method performs a quick scan of all attached contexts to provide
    /// aggregated statistics, useful for logging, analytics, or deciding
    /// on error handling strategies.
    ///
    /// # Returns
    ///
    /// A `ContextAnalysis` struct containing various metrics about the contexts.
    pub fn analyze_contexts(&self) -> ContextAnalysis {
        let mut analysis = ContextAnalysis {
            total_contexts: self.contexts.len(),
            context_depth: self.contexts.len(), // Simple depth = count for now
            ..ContextAnalysis::default()
        };

        for ctx in &self.contexts {
            if ctx.suggestion.is_some() {
                analysis.has_suggestions = true;
            }
            if ctx.location.is_some() {
                analysis.has_location_info = true;
            }
            analysis.metadata_entries += ctx.metadata.len();
            analysis.typed_payloads += ctx.payloads.len();

            // The primary context is the last one in the vector
            if let Some(primary_ctx) = self.contexts.last() {
                analysis.primary_context_priority = primary_ctx.priority;
            }
        }
        analysis
    }
}

impl Display for Yoshi {
    /// Enhanced display implementation with full context formatting.
    fn fmt(&self, f: &mut Formatter<'_>) -> fmt::Result {
        // Start with the primary error kind
        write!(f, "{}", self.kind)?;

        // Append the chain of contexts attached to *this* Yoshi instance
        for ctx in &self.contexts {
            // Skip auto-generated, empty contexts to keep the output clean
            if ctx.message.is_none()
                && ctx.suggestion.is_none()
                && ctx.metadata.is_empty()
                && ctx.payloads.is_empty()
            {
                continue;
            }

            if let Some(msg) = ctx.message.as_deref() {
                write!(f, "\n  - Caused by: {msg}")?;

                if let Some(loc) = ctx.location {
                    write!(f, " (at {loc})")?;
                }
            }
        }
        Ok(())
    }
}

//============================================================================
// SECTION 6: CONVERSION IMPLEMENTATIONS FOR STD
//============================================================================

impl From<std::io::Error> for Yoshi {
    /// Converts a `std::io::Error` into a `Yoshi` error.
    ///
    /// The I/O error is wrapped in a `YoshiKind::StdIo` variant when the std feature
    /// is enabled, preserving the original error information and enabling it to participate
    /// in the Yoshi error ecosystem.
    #[track_caller]
    fn from(e: std::io::Error) -> Self {
        Yoshi::new(YoshiKind::StdIo(e))
    }
}

//============================================================================
// SECTION 7: ENHANCED DEBUGGING HELPERS
//============================================================================

/// Helper struct to store std::backtrace information
#[derive(Debug)]
struct StdBacktraceInfo {
    std_backtrace: std::backtrace::Backtrace,
    capture_thread: Option<String>,
}

//============================================================================
// SECTION 8: ENHANCED YUM MACRO FOR STD
//============================================================================

/// Enhanced debug macro for std environments with backtrace support.
///
/// This macro provides comprehensive error debugging output, displaying the complete
/// error information including context chains, metadata, and backtrace information.
/// The name `yum!` reflects Yoshi's characteristic eating behavior while providing
/// memorable, intuitive debugging functionality.
///
/// # Arguments
///
/// * `$err` - A reference to a `Yoshi` error or any expression that evaluates to one
///
/// # Output Format
///
/// The macro produces structured output including:
/// - Error instance ID for correlation
/// - Primary error message and kind
/// - Complete context chain with metadata
/// - Source error information if available
/// - Backtrace information (when enabled)
///
/// # Examples
///
/// ```rust
/// use yoshi_std::{yum, Yoshi, YoshiKind};
///
/// let err = Yoshi::new(YoshiKind::Internal {
///     message: "database connection failed".into(),
///     source: None,
///     component: None,
/// });
///
/// yum!(err);  // Prints comprehensive error information
/// ```
#[macro_export]
macro_rules! yum {
    ($err:expr) => {{
        let _y: &$crate::Yoshi = &$err;
        eprintln!("🍽️  Yoshi consumed error [{}]: {}", _y.instance_id, _y);

        // Display enhanced error information
        if let Some(_laytext) = _y.laytext() {
            eprintln!("   📝 Context: {}", _laytext);
        }

        if let Some(_suggestion) = _y.suggestion() {
            eprintln!("   💡 Suggestion: {}", _suggestion);
        }

        if let Some(_nest) = _y.nest() {
            eprintln!("   🥚 Nested: {}", _nest);
        }

        // Display backtrace if available
        if let Some(bt) = _y.backtrace() {
            eprintln!("   📚 Backtrace:\n{}", bt);
        }

        // Analysis information
        let analysis = _y.analyze_contexts();
        if analysis.total_contexts > 0 {
            eprintln!(
                "   📊 Analysis: {} contexts, {} metadata entries, severity: {}",
                analysis.total_contexts,
                analysis.metadata_entries,
                _y.severity()
            );
        }

        _y
    }};
}

//============================================================================
// SECTION 9: COMPREHENSIVE TEST SUITE
//============================================================================

#[cfg(test)]
mod tests {
    use super::*;
    use std::io::{Error, ErrorKind};

    #[test]
    fn test_std_io_error_conversion() {
        let io_error = Error::new(ErrorKind::NotFound, "file not found");
        let yoshi_error = Yoshi::from(io_error);

        assert!(matches!(yoshi_error.kind, YoshiKind::StdIo(_)));
        assert!(yoshi_error.to_string().contains("not found"));
    }

    #[test]
    fn test_hatchable_trait() {
        let io_result: Result<String, std::io::Error> = Err(std::io::Error::new(
            std::io::ErrorKind::NotFound, "test file not found"
        ));

        let hatched: Hatch<String> = io_result.hatch();
        assert!(hatched.is_err());

        let error = hatched.unwrap_err();
        assert!(matches!(error.kind, YoshiKind::StdIo(_)));
    }

    #[test]
    fn test_lay_text_trait() {
        let result: Hatch<()> = Err(Yoshi::new(YoshiKind::Internal {
            message: "base error".into(),
            source: None,
            component: None,
        }));

        let enriched = result.lay("additional context");
        assert!(enriched.is_err());

        let error = enriched.unwrap_err();
        assert_eq!(error.contexts.len(), 1);
        assert_eq!(error.contexts[0].message.as_deref(), Some("additional context"));
    }

    #[test]
    fn test_hatch_ext_trait() {
        let io_result: Result<String, std::io::Error> = Err(std::io::Error::new(
            std::io::ErrorKind::PermissionDenied, "access denied"
        ));

        let enhanced = io_result
            .context("Failed to read config file")
            .with_suggestion("Check file permissions")
            .with_metadata("file_type", "config")
            .with_priority(200);

        assert!(enhanced.is_err());
        let error = enhanced.unwrap_err();

        // Should have one context from .context()
        assert_eq!(error.contexts.len(), 1);
        let ctx = &error.contexts[0];
        assert_eq!(ctx.message.as_deref(), Some("Failed to read config file"));
        assert_eq!(ctx.suggestion.as_deref(), Some("Check file permissions"));
        assert_eq!(ctx.metadata.get("file_type").unwrap().as_ref(), "config");
        assert_eq!(ctx.priority, 200);
    }

    #[test]
    fn test_yoshi_methods() {
        let error = Yoshi::new(YoshiKind::Internal {
            message: "base error".into(),
            source: None,
            component: None,
        })
        .context("first context")
        .with_suggestion("try this")
        .with_metadata("key", "value")
        .with_component("auth_service")
        .with_priority(250);

        assert_eq!(error.severity(), 80); // Internal error severity
        assert!(!error.is_transient());
        assert_eq!(error.contexts.len(), 1);

        let ctx = error.primary_context().unwrap();
        assert_eq!(ctx.message.as_deref(), Some("first context"));
        assert_eq!(ctx.suggestion.as_deref(), Some("try this"));
        assert_eq!(ctx.priority, 250);
        assert_eq!(ctx.metadata.get("key").unwrap().as_ref(), "value");
        assert_eq!(ctx.metadata.get("component").unwrap().as_ref(), "auth_service");
    }

    #[test]
    fn test_foreign_error_wrapping() {
        #[derive(Debug)]
        struct CustomError(&'static str);

        impl std::fmt::Display for CustomError {
            fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
                write!(f, "CustomError: {}", self.0)
            }
        }

        impl std::error::Error for CustomError {}

        let foreign_err = CustomError("external failure");
        let yoshi_err = Yoshi::foreign(foreign_err);

        assert!(matches!(yoshi_err.kind, YoshiKind::Foreign { .. }));
        assert!(yoshi_err.to_string().contains("CustomError"));

        // Test downcasting
        let downcasted = yoshi_err.downcast_ref::<CustomError>();
        assert!(downcasted.is_some());
        assert_eq!(downcasted.unwrap().0, "external failure");
    }

    #[test]
    fn test_context_analysis() {
        let error = Yoshi::new(YoshiKind::Internal {
            message: "base error".into(),
            source: None,
            component: None,
        })
        .context("first context")
        .with_suggestion("try this")
        .with_metadata("key1", "value1")
        .context("second context")
        .with_metadata("key2", "value2")
        .with_shell(42u32);

        let analysis = error.analyze_contexts();
        assert_eq!(analysis.total_contexts, 2);
        assert_eq!(analysis.context_depth, 2);
        assert!(analysis.has_suggestions);
        assert!(analysis.has_location_info);
        assert_eq!(analysis.metadata_entries, 2); // key1 and key2
        assert_eq!(analysis.typed_payloads, 1); // The u32 shell
    }

    #[test]
    fn test_shell_retrieval() {
        #[derive(Debug, PartialEq)]
        struct TestPayload {
            value: u32,
        }

        let error = Yoshi::new(YoshiKind::Internal {
            message: "base error".into(),
            source: None,
            component: None,
        })
        .with_shell(TestPayload { value: 123 })
        .with_shell("string payload".to_string());

        let payload = error.shell::<TestPayload>();
        assert!(payload.is_some());
        assert_eq!(payload.unwrap().value, 123);

        let string_payload = error.shell::<String>();
        assert!(string_payload.is_some());
        assert_eq!(string_payload.unwrap(), "string payload");

        let missing = error.shell::<f64>();
        assert!(missing.is_none());
    }

    #[test]
    fn test_thematic_methods() {
        let error = Yoshi::new(YoshiKind::Internal {
            message: "base error".into(),
            source: None,
            component: None,
        })
        .lay("thematic context");

        assert_eq!(error.laytext(), Some("thematic context"));
        assert_eq!(error.contexts.len(), 1);

        // nest() should return None for this error since there's no source
        assert!(error.nest().is_none());
    }
}
