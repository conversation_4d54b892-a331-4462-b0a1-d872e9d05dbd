/* yoshi-deluxe/src/types.rs */
//! **Brief:** Core data structures and type definitions for yoshi-deluxe.
//!
//! This module contains all the fundamental data structures used throughout the
//! auto-correction system, including diagnostic information, AST context, correction
//! proposals, and system configuration types with comprehensive validation.

use serde::{Deserialize, Serialize};
use std::{
    collections::HashMap,
    fmt,
    path::PathBuf,
    sync::{atomic::AtomicU64, Arc},
    time::SystemTime,
};

//--------------------------------------------------------------------------------------------------
// Compiler Diagnostic Types
//--------------------------------------------------------------------------------------------------

/// Comprehensive representation of a compiler diagnostic with enhanced metadata
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CompilerDiagnostic {
    /// Unique diagnostic identifier for tracking
    pub id: String,
    /// Error message content
    pub message: String,
    /// Error code (e.g., "E0599")
    pub code: Option<String>,
    /// Severity level
    pub level: DiagnosticLevel,
    /// File location information with precise mapping
    pub spans: Vec<DiagnosticSpan>,
    /// Child diagnostics with suggestions
    pub children: Vec<CompilerDiagnostic>,
    /// Suggested replacements from compiler
    pub suggested_replacement: Option<String>,
    /// Additional metadata for correction context
    pub metadata: HashMap<String, String>,
    /// Diagnostic creation timestamp
    pub created_at: SystemTime,
    /// Whether this diagnostic has been processed
    pub processed: bool,
}

impl CompilerDiagnostic {
    /// Create a new diagnostic with basic information
    #[must_use]
    pub fn new(id: impl Into<String>, message: impl Into<String>, level: DiagnosticLevel) -> Self {
        Self {
            id: id.into(),
            message: message.into(),
            code: None,
            level,
            spans: Vec::new(),
            children: Vec::new(),
            suggested_replacement: None,
            metadata: HashMap::new(),
            created_at: SystemTime::now(),
            processed: false,
        }
    }

    /// Get the primary span for this diagnostic
    #[must_use]
    pub fn primary_span(&self) -> Option<&DiagnosticSpan> {
        self.spans
            .iter()
            .find(|span| span.is_primary)
            .or_else(|| self.spans.first())
    }

    /// Check if this diagnostic represents an error
    #[must_use]
    pub const fn is_error(&self) -> bool {
        matches!(self.level, DiagnosticLevel::Error)
    }

    /// Get a short description for this diagnostic
    #[must_use]
    pub fn short_description(&self) -> String {
        format!(
            "{}: {}",
            self.level,
            self.message.chars().take(100).collect::<String>()
        )
    }

    /// Add metadata to the diagnostic
    pub fn add_metadata(&mut self, key: impl Into<String>, value: impl Into<String>) {
        self.metadata.insert(key.into(), value.into());
    }

    /// Mark diagnostic as processed
    pub fn mark_processed(&mut self) {
        self.processed = true;
    }
}

/// Enhanced diagnostic severity levels with priority scoring
#[derive(Debug, Clone, Copy, Serialize, Deserialize, PartialEq, Eq, PartialOrd, Ord)]
pub enum DiagnosticLevel {
    /// Critical errors that prevent compilation
    Error,
    /// Warnings that should be addressed
    Warning,
    /// Informational notes
    Note,
    /// Help suggestions
    Help,
}

impl DiagnosticLevel {
    /// Get numeric priority for this level
    #[must_use]
    pub const fn priority(&self) -> u8 {
        match self {
            Self::Error => 255,
            Self::Warning => 128,
            Self::Note => 64,
            Self::Help => 32,
        }
    }
}

impl fmt::Display for DiagnosticLevel {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            Self::Error => write!(f, "error"),
            Self::Warning => write!(f, "warning"),
            Self::Note => write!(f, "note"),
            Self::Help => write!(f, "help"),
        }
    }
}

/// Precise source code location with enhanced mapping capabilities
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DiagnosticSpan {
    /// Source file path (canonicalized)
    pub file_name: PathBuf,
    /// Byte-level start position (0-indexed)
    pub byte_start: usize,
    /// Byte-level end position (0-indexed)
    pub byte_end: usize,
    /// Line number (1-indexed)
    pub line_start: usize,
    /// Line number (1-indexed)
    pub line_end: usize,
    /// Column number (1-indexed)
    pub column_start: usize,
    /// Column number (1-indexed)
    pub column_end: usize,
    /// Actual text content
    pub text: String,
    /// Primary span indicator
    pub is_primary: bool,
    /// Span label if available
    pub label: Option<String>,
    /// Expansion information for macro spans
    pub expansion: Option<Box<DiagnosticSpan>>,
}

impl DiagnosticSpan {
    /// Create a new diagnostic span
    #[must_use]
    pub fn new(
        file_name: PathBuf,
        byte_start: usize,
        byte_end: usize,
        line_start: usize,
        line_end: usize,
        column_start: usize,
        column_end: usize,
        text: String,
    ) -> Self {
        Self {
            file_name,
            byte_start,
            byte_end,
            line_start,
            line_end,
            column_start,
            column_end,
            text,
            is_primary: false,
            label: None,
            expansion: None,
        }
    }

    /// Mark this span as primary
    pub fn mark_primary(&mut self) {
        self.is_primary = true;
    }

    /// Set the label for this span
    pub fn set_label(&mut self, label: impl Into<String>) {
        self.label = Some(label.into());
    }
}

//--------------------------------------------------------------------------------------------------
// Documentation and API Types
//--------------------------------------------------------------------------------------------------

/// Cached documentation data with intelligent expiration and versioning
#[derive(Debug, Clone)]
pub struct CachedDocsData {
    /// Cache format version for compatibility
    pub version: u32,
    /// Target crate information
    pub crate_info: CrateInfo,
    /// API method signatures with enhanced metadata
    pub methods: Vec<MethodSignature>,
    /// Implementation details with trait mappings
    pub implementations: Vec<TraitImplementation>,
    /// Usage examples with context
    pub examples: Vec<CodeExample>,
    /// Cache creation timestamp
    pub cached_at: SystemTime,
    /// Cache access count for LRU eviction
    pub access_count: Arc<AtomicU64>,
    /// Data source for provenance tracking
    pub source: DataSource,
}

impl CachedDocsData {
    /// Create new cached docs data
    #[must_use]
    pub fn new(
        crate_info: CrateInfo,
        methods: Vec<MethodSignature>,
        implementations: Vec<TraitImplementation>,
        examples: Vec<CodeExample>,
        source: DataSource,
    ) -> Self {
        Self {
            version: 1,
            crate_info,
            methods,
            implementations,
            examples,
            cached_at: SystemTime::now(),
            access_count: Arc::new(AtomicU64::new(1)),
            source,
        }
    }
}

/// Crate information for documentation context
#[derive(Debug, Clone)]
pub struct CrateInfo {
    /// Crate name
    pub name: String,
}

/// Data source enumeration for provenance
#[derive(Debug, Clone)]
pub enum DataSource {
    /// docs.rs HTML scraping
    DocsRs {
        /// The URL that was scraped
        url: String,
    },
}

/// Method signature with comprehensive metadata and validation
#[derive(Debug, Clone)]
pub struct MethodSignature {
    /// Method name
    pub name: String,
    /// Parameter types and names with defaults
    pub parameters: Vec<Parameter>,
    /// Return type with full path
    pub return_type: Option<String>,
    /// Documentation string (cleaned)
    pub documentation: String,
}

impl MethodSignature {
    /// Generate a canonical signature string for comparison
    #[must_use]
    pub fn canonical_signature(&self) -> String {
        let params = self
            .parameters
            .iter()
            .map(|p| format!("{}: {}", p.name, p.param_type))
            .collect::<Vec<_>>()
            .join(", ");

        let return_part = self
            .return_type
            .as_ref()
            .map_or_else(String::new, |rt| format!(" -> {rt}"));

        format!("{}({}){}", self.name, params, return_part)
    }
}

/// Function parameter with enhanced type information
#[derive(Debug, Clone)]
pub struct Parameter {
    /// Parameter name
    pub name: String,
    /// Parameter type with full path
    pub param_type: String,
}

impl Parameter {
    /// Create a new parameter
    #[must_use]
    pub fn new(name: impl Into<String>, param_type: impl Into<String>) -> Self {
        Self {
            name: name.into(),
            param_type: param_type.into(),
        }
    }
}

/// Trait implementation details with enhanced metadata
#[derive(Debug, Clone)]
pub struct TraitImplementation {
    /// Trait name with full path
    pub trait_name: String,
}

/// Code example with enhanced context and validation
#[derive(Debug, Clone)]
pub struct CodeExample {
    /// Example code content
    pub code: String,
}

//--------------------------------------------------------------------------------------------------
// Correction Types and Strategies
//--------------------------------------------------------------------------------------------------

/// Comprehensive correction proposal with safety metadata
#[derive(Debug, Clone)]
pub struct CorrectionProposal {
    /// Original problematic code
    pub original_code: String,
    /// Suggested corrected code
    pub corrected_code: String,
    /// Confidence score (0.0-1.0)
    pub confidence: f64,
    /// Correction strategy used
    pub strategy: CorrectionStrategy,
    /// Supporting documentation
    pub documentation_source: Option<String>,
    /// Additional context metadata
    pub context_metadata: HashMap<String, String>,
    /// Byte range for precise application
    pub byte_range: (usize, usize),
    /// Safety level of this correction
    pub safety_level: SafetyLevel,
}

impl CorrectionProposal {
    /// Create a new correction proposal
    #[must_use]
    pub fn new(
        original_code: impl Into<String>,
        corrected_code: impl Into<String>,
        confidence: f64,
        strategy: CorrectionStrategy,
    ) -> Self {
        Self {
            original_code: original_code.into(),
            corrected_code: corrected_code.into(),
            confidence,
            strategy,
            documentation_source: None,
            context_metadata: HashMap::new(),
            byte_range: (0, 0),
            safety_level: SafetyLevel::RequiresReview,
        }
    }

    /// Check if this proposal is considered safe for automatic application
    #[must_use]
    pub const fn is_auto_applicable(&self) -> bool {
        matches!(self.safety_level, SafetyLevel::Safe) && self.confidence > 0.9
    }

    /// Get a description of the correction strategy
    #[must_use]
    pub fn strategy_description(&self) -> String {
        match &self.strategy {
            CorrectionStrategy::MethodNameCorrection { similarity_score } => {
                format!(
                    "Method name correction (similarity: {:.2})",
                    similarity_score
                )
            }
            CorrectionStrategy::TypeConversion {
                from_type, to_type, ..
            } => {
                format!("Type conversion from {from_type} to {to_type}")
            }
            CorrectionStrategy::ImportAddition { import_path } => {
                format!("Add import: {import_path}")
            }
            CorrectionStrategy::TraitImport {
                trait_name,
                method_name,
            } => {
                format!("Import trait {trait_name} for method {method_name}")
            }
            CorrectionStrategy::Generic { description } => description.clone(),
            _ => "Code correction".to_string(),
        }
    }

    /// Add metadata to the proposal
    pub fn add_metadata(&mut self, key: impl Into<String>, value: impl Into<String>) {
        self.context_metadata.insert(key.into(), value.into());
    }

    /// Set the safety level
    pub fn set_safety_level(&mut self, level: SafetyLevel) {
        self.safety_level = level;
    }

    /// Set byte range for application
    pub fn set_byte_range(&mut self, start: usize, end: usize) {
        self.byte_range = (start, end);
    }
}

/// Enhanced correction strategies with comprehensive classification
#[derive(Debug, Clone)]
pub enum CorrectionStrategy {
    /// Method name correction with similarity metrics
    MethodNameCorrection {
        /// Similarity score
        similarity_score: f64,
    },
    /// Type conversion with method specification
    TypeConversion {
        /// Original type
        from_type: String,
        /// Target type
        to_type: String,
        /// Method used for conversion
        conversion_method: String,
    },
    /// Reference/dereference operation
    ReferenceCorrection {
        /// The operation performed
        operation: String,
    },
    /// Import addition
    ImportAddition {
        /// Path of the import to add
        import_path: String,
    },
    /// Trait import for method access
    TraitImport {
        /// Name of the trait to import
        trait_name: String,
        /// Name of the method enabled by the trait
        method_name: String,
    },
    /// Borrowing and lifetime correction
    BorrowingCorrection {
        /// The operation performed
        operation: String,
    },
    /// Generic correction with description
    Generic {
        /// A description of the correction
        description: String,
    },
}

/// Safety level classification for corrections
#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord)]
pub enum SafetyLevel {
    /// Safe to apply automatically
    Safe,
    /// Requires manual review before application
    RequiresReview,
    /// Potentially unsafe, should not be auto-applied
    Unsafe,
}

impl fmt::Display for SafetyLevel {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            Self::Safe => write!(f, "safe"),
            Self::RequiresReview => write!(f, "requires review"),
            Self::Unsafe => write!(f, "unsafe"),
        }
    }
}

/// Suggestion for a field access correction
#[derive(Debug, Clone)]
pub struct FieldSuggestion {
    /// Suggested field name
    pub name: String,
    /// Confidence in this suggestion
    pub confidence: f64,
    /// Description of the suggestion
    pub description: String,
}

/// Enhanced method suggestion with comprehensive metadata
#[derive(Debug, Clone)]
pub struct MethodSuggestion {
    /// Suggested method name
    pub method_name: String,
    /// Similarity score (0.0-1.0)
    pub similarity_score: f64,
    /// Method signature
    pub signature: String,
    /// Method documentation
    pub documentation: String,
}

//--------------------------------------------------------------------------------------------------
// System Results and Tracking
//--------------------------------------------------------------------------------------------------

/// Complete correction information for a project file
#[derive(Debug, Clone)]
pub struct ProjectCorrection {
    /// File path that needs correction
    pub file_path: PathBuf,
    /// Original diagnostic that triggered the correction
    pub diagnostic: CompilerDiagnostic,
    /// Generated correction proposals
    pub proposals: Vec<CorrectionProposal>,
    /// Creation timestamp
    pub created_at: SystemTime,
    /// Whether correction has been applied
    pub applied: bool,
}

impl ProjectCorrection {
    /// Create new project correction
    #[must_use]
    pub fn new(file_path: PathBuf, diagnostic: CompilerDiagnostic) -> Self {
        Self {
            file_path,
            diagnostic,
            proposals: Vec::new(),
            created_at: SystemTime::now(),
            applied: false,
        }
    }

    /// Get the best (highest confidence) proposal
    #[must_use]
    pub fn best_proposal(&self) -> Option<&CorrectionProposal> {
        self.proposals.first()
    }

    /// Add a proposal to this correction
    pub fn add_proposal(&mut self, proposal: CorrectionProposal) {
        self.proposals.push(proposal);
        // Keep proposals sorted by confidence
        self.proposals.sort_by(|a, b| {
            b.confidence
                .partial_cmp(&a.confidence)
                .unwrap_or(std::cmp::Ordering::Equal)
        });
    }
}

/// Record of an applied correction with backup information
#[derive(Debug, Clone)]
pub struct AppliedCorrection {
    /// File path that was corrected
    pub file_path: PathBuf,
    /// Original problematic code
    pub original_code: String,
    /// Applied corrected code
    pub corrected_code: String,
    /// Strategy used for the correction
    pub strategy: CorrectionStrategy,
    /// Application timestamp
    pub applied_at: SystemTime,
    /// Backup file path
    pub backup_path: Option<PathBuf>,
}

impl AppliedCorrection {
    /// Create new applied correction
    #[must_use]
    pub fn new(
        file_path: PathBuf,
        original_code: String,
        corrected_code: String,
        strategy: CorrectionStrategy,
    ) -> Self {
        Self {
            file_path,
            original_code,
            corrected_code,
            strategy,
            applied_at: SystemTime::now(),
            backup_path: None,
        }
    }

    /// Set backup path
    pub fn set_backup_path(&mut self, path: PathBuf) {
        self.backup_path = Some(path);
    }
}

//--------------------------------------------------------------------------------------------------
// Configuration Types
//--------------------------------------------------------------------------------------------------

/// Enhanced system configuration with production settings
#[derive(Debug, Clone)]
pub struct SystemConfig {
    /// Maximum correction proposals per diagnostic
    pub max_proposals_per_diagnostic: usize,
    /// Minimum confidence threshold for proposals
    pub min_confidence_threshold: f64,
    /// Enable parallel processing
    pub enable_parallel_processing: bool,
    /// Cache size limits
    pub max_cache_size: usize,
    /// Documentation scraping enabled
    pub enable_docs_scraping: bool,
    /// Maximum concurrent operations
    pub max_concurrent_operations: usize,
    /// Safety level filter
    pub min_safety_level: SafetyLevel,
    /// Enable metrics collection
    pub enable_metrics: bool,
    /// Auto-apply safe corrections
    pub auto_apply_safe_corrections: bool,
    /// Create backup files
    pub create_backup_files: bool,
}

impl Default for SystemConfig {
    fn default() -> Self {
        Self {
            max_proposals_per_diagnostic: 3,
            min_confidence_threshold: 0.6,
            enable_parallel_processing: true,
            max_cache_size: 500,
            enable_docs_scraping: true,
            max_concurrent_operations: 6,
            min_safety_level: SafetyLevel::RequiresReview,
            enable_metrics: true,
            auto_apply_safe_corrections: false,
            create_backup_files: true,
        }
    }
}

impl SystemConfig {
    /// Validate configuration parameters
    pub fn validate(&self) -> crate::Result<()> {
        use crate::errors::factory;

        if self.max_proposals_per_diagnostic == 0 {
            return Err(factory::configuration_error(
                "max_proposals_per_diagnostic",
                "0",
            ));
        }

        if !(0.0..=1.0).contains(&self.min_confidence_threshold) {
            return Err(factory::configuration_error(
                "min_confidence_threshold",
                self.min_confidence_threshold.to_string(),
            ));
        }

        if self.max_concurrent_operations == 0 {
            return Err(factory::configuration_error(
                "max_concurrent_operations",
                "0",
            ));
        }

        Ok(())
    }
}
