[package]
name = "yoshi-deluxe"
version = "0.1.6"
edition = "2021"
rust-version = "1.87.0"
authors = ["<PERSON> <<PERSON><PERSON><PERSON>@proton.me>"]
repository = "https://github.com/arcmoonstudios/yoshi"
license = "MIT OR Apache-2.0"
description = "Advanced LSP server, error analysis engine, and runtime diagnostics for the Yoshi error handling framework."
keywords = ["lsp", "language-server", "error-analysis", "diagnostics", "yoshi"]
categories = ["development-tools", "debugging", "parser-implementations"]
readme = "README.md"

[dependencies]
# Core Yoshi framework dependencies
yoshi-std = { version = "0.1.6", path = "../yoshi-std" }
yoshi-derive = { version = "0.1.6", path = "../yoshi-derive" }

# Core pattern matching and analysis
regex = "1.11.1"
lazy_static = "1.5.0"

# AST parsing and code generation
syn = { version = "2.0.101", features = [
    "full",
    "parsing",
    "visit",
    "visit-mut",
    "extra-traits",
] }
quote = "1.0.40"
proc-macro2 = { version = "1.0.95", features = ["span-locations"] }

# HTTP client for docs scraping
reqwest = { version = "0.12.19", features = [
    "json",
    "rustls-tls",
], default-features = false }

# HTML parsing for documentation extraction
scraper = "0.20"

# Configuration management (core feature)
toml = { version = "0.8.23", features = ["preserve_order"] }
toml_edit = { version = "0.22.22", features = ["serde"] }
dirs = "5.0"

# Serialization support (core features)
serde = { version = "1.0.215", features = ["derive"] }
serde_json = { version = "1.0.133" }

# Async runtime and utilities
tokio = { version = "1.45.1", features = [
    "rt",
    "rt-multi-thread",
    "sync",
    "io-std",
    "io-util",
    "net",
    "time",
    "process",
    "signal",
    "fs",
    "macros",
] }
tokio-util = { version = "0.7.15", features = ["compat"] }

# Additional core dependencies
futures = "0.3.31"
dashmap = "6.1.0"

# Code formatting for AST-to-string conversion
prettyplease = "0.2"

# String similarity calculations
strsim = "0.11"

# System information
num_cpus = "1.16"

# Temporary files for testing
tempfile = "3.13.0"

# LSP server implementation
tower-lsp = { version = "0.20.0", optional = true }
url = { version = "2.5.0", features = ["serde"], optional = true }

# CLI support
clap = { version = "4.5.21", features = [
    "derive",
    "env",
    "unicode",
    "color",
], optional = true }

# Observability and logging
tracing = { version = "0.1.41", optional = true }
tracing-subscriber = { version = "0.3.19", features = [
    "env-filter",
    "json",
    "ansi",
], optional = true }

# Performance monitoring
rayon = { version = "1.8.0", optional = true }

# Time handling
chrono = { version = "0.4.38", features = ["serde"], optional = true }

# System integration utilities
which = { version = "8.0.0", optional = true }
shellexpand = { version = "3.1.1", optional = true }

# Configuration validation and schema
schemars = { version = "0.8", features = ["preserve_order"], optional = true }

# File watching for config hot-reload
notify = { version = "6.1", optional = true }

[features]
default = [
    "runtime-analysis",
    "tracing",
    "lsp-integration",
    "cli",
    "config-validation",
]

# Core features
runtime-analysis = []
config-validation = ["dep:schemars"]
config-hot-reload = ["dep:notify"]

# Complete LSP server implementation
lsp-integration = ["dep:tower-lsp", "dep:url", "dep:chrono", "tracing"]

# CLI tools and server management
cli = ["dep:clap", "dep:which", "dep:shellexpand", "tracing"]

# CLI examples
examples = []

# Benchmarking and performance testing
benchmarks = []

# Observability and monitoring
tracing = ["dep:tracing", "dep:tracing-subscriber"]

# Performance monitoring and metrics
performance-monitoring = ["dep:rayon", "dep:chrono"]

# Enhanced configuration features
config-advanced = [
    "config-validation",
    "config-hot-reload",
    "performance-monitoring",
]

# Complete feature set (useful for development and testing)
full = ["lsp-integration", "cli", "performance-monitoring", "config-advanced"]

[lib]
# This is a regular library crate, not a proc-macro
proc-macro = false

# docs.rs specific configuration
[package.metadata.docs.rs]
rustc-args = ["--cap-lints=warn"]
features = ["full"]
no-default-features = false
rustdoc-args = ["--cfg", "docsrs"]
targets = ["x86_64-unknown-linux-gnu"]

[dev-dependencies]
# Testing dependencies
tokio-test = "0.4.4"
pretty_assertions = "1.4.1"
tempfile = "3.13.0"
criterion = { version = "0.5.1", features = ["html_reports"] }

# Integration testing with tower-lsp
tower = { version = "0.5.1", features = ["util"] }

# Configuration testing utilities
insta = "1.34"    # for snapshot testing of config parsing
assert_fs = "1.1" # for filesystem fixture testing
