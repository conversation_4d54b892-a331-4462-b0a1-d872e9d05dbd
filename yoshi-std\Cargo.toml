[package]
name = "yoshi-std"
version = "0.1.6"
edition = "2021"
rust-version = "1.87.0"                                               # MSRV
authors = ["<PERSON> <<PERSON><PERSON><PERSON>@proton.me>"]
repository = "https://github.com/arcmoonstudios/yoshi"
license = "MIT OR Apache-2.0"
description = "Core, std-only error type for the Yoshi framework."
keywords = ["error", "error-handling", "yoshi", "std-only", "no-std"]
categories = ["development-tools", "rust-patterns"]

[dependencies]
yoshi-core = { path = "../yoshi-core", version = "0.1.6", default-features = false }
serde = { version = "1.0.219", optional = true, features = ["derive"] }
serde_json = { version = "1.0.140", optional = true }
tracing = { version = "0.1.41", optional = true }
miette = { version = "7.6.0", optional = true }
tokio = { version = "1.45.1", optional = true, features = ["time"] }

[features]
default = ["std"]
std = ["yoshi-core/std"]
derive = []                                                 # pass-through flag
serde = ["dep:serde", "dep:serde_json", "yoshi-core/serde"]
tracing = ["dep:tracing"]
async = ["dep:tokio"]
# Stable SIMD optimizations using std::arch (no unstable features)
simd-optimized = []
# LSP integration support (placeholder for compatibility)
lsp-integration = []

[lib]
name = "yoshi_std"
path = "src/lib.rs"

# docs.rs specific configuration for robust builds
[package.metadata.docs.rs]
# CRITICAL: Conservative feature set - NO experimental features
features = ["std", "serde", "tracing"]
no-default-features = false
# CRITICAL: Force stable toolchain, disable experimental features
rustdoc-args = ["--cfg", "docsrs"]
# Conservative rustc args for stable compatibility
rustc-args = ["--cap-lints=warn"]
targets = ["x86_64-unknown-linux-gnu"]
# FORCE stable toolchain - this should prevent nightly
default-target = "x86_64-unknown-linux-gnu"
