/* yoshi-derive/tests/compilation_tests.rs */
#![warn(missing_docs)]
#![warn(clippy::cargo)]
#![deny(unsafe_code)]

//! **Brief:** Compilation validation and edge case tests for YoshiError derive macro
//!
//! These tests ensure that the macro generates valid Rust code and handles edge cases
//! gracefully. Tests include boundary conditions, unusual configurations, and error
//! recovery scenarios.

// ~=####====A===r===c===M===o===o===n====S===t===u===d===i===o===s====X|0|$>
//! + [Compilation Validation and Edge Case Testing]
//!  - [Boundary condition testing for complex configurations]
//!  - [Error recovery and fallback implementation validation]
//!  - [Generated code quality and performance verification]
//!  - [Trait bound inference and generic constraint validation]
// ~=####====A===r===c===M===o===o===n====S===t===u===d===i===o===s====X|0|$>
// **GitHub:** [ArcMoon Studios](https://github.com/arcmoonstudios)
// **Copyright:** (c) 2025 ArcMoon Studios
// **License:** MIT OR Apache-2.0
// **Contact:** <EMAIL>
// **Author:** Lord Xyn

use std::error::Error;
use std::fmt::{Debug, Display};
use yoshi_derive::YoshiError;

//--------------------------------------------------------------------------------------------------
// Minimal Configuration Tests
//--------------------------------------------------------------------------------------------------

#[derive(Debug, YoshiError)]
enum MinimalError {
    Basic,
}

#[test]
fn test_minimal_error_works() {
    let err = MinimalError::Basic;
    assert_eq!(format!("{}", err), "Basic");
    assert_eq!(err.variant_name(), "Basic");
    assert_eq!(err.error_kind(), "Internal"); // Default inferred kind
}

//--------------------------------------------------------------------------------------------------
// Empty Enum Edge Case
//--------------------------------------------------------------------------------------------------

// Note: This should actually fail to compile with a helpful error message
// Uncomment to test error handling:
/*
#[derive(Debug, YoshiError)]
enum EmptyError {
    // This should produce a compilation error
}
*/

//--------------------------------------------------------------------------------------------------
// Maximum Complexity Configuration
//--------------------------------------------------------------------------------------------------

#[derive(Debug, YoshiError)]
#[yoshi(
    default_severity = 100,
    default_kind = "Custom",
    optimize_large = true,
    auto_inference = true,
    generate_helpers = true,
    namespace = "complex",
    error_code_base = 5000,
    strict_validation = true,
    debug = false,
    override_codes = false
)]
enum MaxComplexityError {
    #[yoshi(
        display = "Ultra complex error: {operation} failed with {details} (code: {error_code:?})",
        kind = "UltraComplex",
        severity = 255,
        suggestion = "This is an ultra-complex error requiring comprehensive handling",
        transient = true,
        transparent = false,
        from = false,
        skip = false,
        code = 5001,
        category = "ultra-complex",
        doc_url = "https://docs.example.com/ultra-complex-error"
    )]
    UltraComplex {
        operation: String,
        details: String,
        error_code: Option<u32>,
        #[yoshi(
            source = false,
            backtrace = false,
            context = "operation_context",
            shell = false,
            skip = false,
            sensitive = false,
            format_with = "format_custom_field",
            transform = "transform_custom_field"
        )]
        custom_field: String,
        #[yoshi(context = "metadata")]
        metadata: std::collections::HashMap<String, String>,
    },
}

fn format_custom_field(field: &String) -> String {
    format!("formatted({})", field)
}

fn transform_custom_field(field: &String) -> String {
    field.to_uppercase()
}

#[test]
fn test_max_complexity_error() {
    let mut metadata = std::collections::HashMap::new();
    metadata.insert("key".to_string(), "value".to_string());

    let err = MaxComplexityError::UltraComplex {
        operation: "test_op".to_string(),
        details: "test_details".to_string(),
        error_code: Some(12345),
        custom_field: "custom_value".to_string(),
        metadata,
    };

    assert_eq!(err.error_kind(), "UltraComplex");
    assert_eq!(err.severity(), 255);
    assert!(err.is_transient());
    assert_eq!(err.error_code(), Some(5001));
    assert!(err.suggestion().is_some());

    let display = format!("{}", err);
    assert!(display.contains("complex:")); // namespace
    assert!(display.contains("test_op"));
    assert!(display.contains("test_details"));
}

//--------------------------------------------------------------------------------------------------
// Unusual Field Types and Patterns
//--------------------------------------------------------------------------------------------------

#[derive(Debug, YoshiError)]
enum UnusualTypesError {
    WithFunction {
        callback: fn() -> String,
    },

    WithClosure {
        #[yoshi(skip)]
        closure: Box<dyn Fn() -> String>,
    },

    WithRawPointer {
        #[yoshi(sensitive)]
        ptr: *const u8,
    },

    WithUnsizedType {
        #[yoshi(context = "slice_info")]
        data: Box<[u8]>,
    },
}

#[test]
fn test_unusual_types() {
    let err = UnusualTypesError::WithFunction {
        callback: || "test".to_string(),
    };

    // Should compile and work even with unusual types
    assert!(!format!("{}", err).is_empty());
    assert_eq!(err.variant_name(), "WithFunction");
}

//--------------------------------------------------------------------------------------------------
// Deep Nesting and Complex Generics
//--------------------------------------------------------------------------------------------------

#[derive(Debug, YoshiError)]
enum NestedGenericError<T, U, E>
where
    T: Debug + Clone,
    U: Display,
    E: Error + Send + Sync + 'static,
{
    Complex {
        data: Vec<Option<Result<T, E>>>,
        formatter: U,
        #[yoshi(source)]
        source: E,
    },

    NestedStruct {
        nested: std::collections::HashMap<String, Vec<(T, U)>>,
    },

    #[yoshi(transparent)]
    Transparent(E),
}

#[test]
fn test_complex_generics() {
    let source_err = std::io::Error::new(std::io::ErrorKind::Other, "test");
    let err = NestedGenericError::Complex {
        data: vec![Some(Ok(42i32))],
        formatter: "test formatter".to_string(),
        source: source_err,
    };

    assert!(err.source().is_some());
    assert!(!format!("{}", err).is_empty());
}

//--------------------------------------------------------------------------------------------------
// Lifetime Edge Cases
//--------------------------------------------------------------------------------------------------

#[derive(Debug, YoshiError)]
enum LifetimeError<'a> {
    WithReference {
        #[yoshi(context = "ref_data")]
        data: &'a str,
    },

    WithStaticRef {
        #[yoshi(source)]
        error: &'static dyn Error,
    },
}

#[test]
fn test_lifetime_handling() {
    let data = "test data";
    let err = LifetimeError::WithReference { data };

    assert!(!format!("{}", err).is_empty());
    assert_eq!(err.variant_name(), "WithReference");
}

//--------------------------------------------------------------------------------------------------
// Unicode and Special Characters
//--------------------------------------------------------------------------------------------------

#[derive(Debug, YoshiError)]
enum UnicodeError {
    #[yoshi(display = "Unicode error: {message} 🚨")]
    WithUnicode { message: String },

    #[yoshi(display = "Special chars: {data}")]
    SpecialChars { data: String },

    #[yoshi(
        display = "Emoji test 🔥🚀⚡",
        suggestion = "Try using ASCII characters only 🤔"
    )]
    EmojiTest,
}

#[test]
fn test_unicode_support() {
    let err = UnicodeError::WithUnicode {
        message: "测试消息".to_string(),
    };

    let display = format!("{}", err);
    assert!(display.contains("🚨"));
    assert!(display.contains("测试消息"));

    let emoji_err = UnicodeError::EmojiTest;
    assert!(emoji_err.suggestion().unwrap().contains("🤔"));
}

//--------------------------------------------------------------------------------------------------
// Error Code Boundary Tests
//--------------------------------------------------------------------------------------------------

#[derive(Debug, YoshiError)]
#[yoshi(error_code_base = u32::MAX - 10)]
enum BoundaryCodeError {
    #[yoshi(code = 0)]
    Zero,

    #[yoshi(code = u32::MAX)]
    MaxValue,

    AutoGenerated1,
    AutoGenerated2,
}

#[test]
fn test_error_code_boundaries() {
    let zero_err = BoundaryCodeError::Zero;
    let max_err = BoundaryCodeError::MaxValue;

    assert_eq!(zero_err.error_code(), Some(0));
    assert_eq!(max_err.error_code(), Some(u32::MAX));

    // Auto-generated codes should work even near boundaries
    let auto1 = BoundaryCodeError::AutoGenerated1;
    assert!(auto1.error_code().is_some());
}

//--------------------------------------------------------------------------------------------------
// Performance Stress Tests
//--------------------------------------------------------------------------------------------------

#[derive(Debug, YoshiError)]
#[yoshi(optimize_large = true)]
enum StressTestError {
    // Many variants to test performance optimizations
    V01,
    V02,
    V03,
    V04,
    V05,
    V06,
    V07,
    V08,
    V09,
    V10,
    V11,
    V12,
    V13,
    V14,
    V15,
    V16,
    V17,
    V18,
    V19,
    V20,
    V21,
    V22,
    V23,
    V24,
    V25,
    V26,
    V27,
    V28,
    V29,
    V30,
    V31,
    V32,
    V33,
    V34,
    V35,
    V36,
    V37,
    V38,
    V39,
    V40,
    V41,
    V42,
    V43,
    V44,
    V45,
    V46,
    V47,
    V48,
    V49,
    V50,
    V51,
    V52,
    V53,
    V54,
    V55,
    V56,
    V57,
    V58,
    V59,
    V60,
    V61,
    V62,
    V63,
    V64,
    V65,
    V66,
    V67,
    V68,
    V69,
    V70,
    V71,
    V72,
    V73,
    V74,
    V75,
    V76,
    V77,
    V78,
    V79,
    V80,
    V81,
    V82,
    V83,
    V84,
    V85,
    V86,
    V87,
    V88,
    V89,
    V90,
    V91,
    V92,
    V93,
    V94,
    V95,
    V96,
    V97,
    V98,
    V99,
    V100,
}

#[test]
fn test_large_enum_performance() {
    use std::time::Instant;

    let start = Instant::now();

    // Test variant name resolution performance
    for _ in 0..1000 {
        let err = StressTestError::V50;
        let _ = err.variant_name();
        let _ = err.error_kind();
        let _ = err.severity();
    }

    let duration = start.elapsed();

    // Should be very fast even with many variants
    assert!(duration.as_millis() < 50);
}

//--------------------------------------------------------------------------------------------------
// Memory Layout and Size Tests
//--------------------------------------------------------------------------------------------------

#[derive(Debug, YoshiError)]
enum SizeTestError {
    Small,
    Medium {
        message: String,
    },
    Large {
        field1: String,
        field2: String,
        field3: String,
        field4: i64,
        field5: f64,
    },
}

#[test]
fn test_memory_efficiency() {
    use std::mem;

    // Basic size checks
    let small_size = mem::size_of::<SizeTestError>();
    println!("SizeTestError size: {} bytes", small_size);

    // Should be reasonably sized
    assert!(small_size <= 64);

    // Test alignment
    assert_eq!(mem::align_of::<SizeTestError>(), mem::align_of::<String>());
}

//--------------------------------------------------------------------------------------------------
// Thread Safety Tests
//--------------------------------------------------------------------------------------------------

#[derive(Debug, YoshiError)]
enum ThreadSafeError {
    #[yoshi(display = "Thread {id}: {message}")]
    ThreadError { id: u64, message: String },

    #[yoshi(from)]
    Sync(Box<dyn Error + Send + Sync>),
}

#[test]
fn test_thread_safety() {
    use std::sync::Arc;
    use std::thread;

    let err = Arc::new(ThreadSafeError::ThreadError {
        id: 123,
        message: "test".to_string(),
    });

    let err_clone = Arc::clone(&err);
    let handle = thread::spawn(move || format!("{}", err_clone));

    let result = handle.join().unwrap();
    assert!(result.contains("Thread 123"));
}

//--------------------------------------------------------------------------------------------------
// Serialization Compatibility Tests (if serde is available)
//--------------------------------------------------------------------------------------------------

#[cfg(feature = "serde")]
mod serde_tests {
    use super::*;
    use serde::{Deserialize, Serialize};

    #[derive(Debug, YoshiError, Serialize, Deserialize)]
    enum SerializableError {
        #[yoshi(display = "Serializable error: {message}")]
        Test { message: String },
    }

    #[test]
    fn test_serde_compatibility() {
        let err = SerializableError::Test {
            message: "test".to_string(),
        };

        let json = serde_json::to_string(&err).unwrap();
        let deserialized: SerializableError = serde_json::from_str(&json).unwrap();

        assert_eq!(format!("{}", err), format!("{}", deserialized));
    }
}

//--------------------------------------------------------------------------------------------------
// Regression Tests for Known Issues
//--------------------------------------------------------------------------------------------------

#[derive(Debug, YoshiError)]
enum RegressionError {
    // Test for issue with empty display strings
    #[yoshi(display = "")]
    EmptyDisplay,

    // Test for issue with very long display strings
    #[yoshi(
        display = "This is a very long display string that should not cause compilation issues even though it exceeds normal length limits and contains many words and detailed descriptions of the error condition"
    )]
    LongDisplay,

    // Test for issue with placeholder edge cases
    #[yoshi(display = "{{{field}}}")]
    WeirdPlaceholders { field: String },
}

#[test]
fn test_regression_cases() {
    let empty = RegressionError::EmptyDisplay;
    assert_eq!(format!("{}", empty), "");

    let long = RegressionError::LongDisplay;
    assert!(format!("{}", long).len() > 100);

    let weird = RegressionError::WeirdPlaceholders {
        field: "test".to_string(),
    };
    assert!(format!("{}", weird).contains("test"));
}

//--------------------------------------------------------------------------------------------------
// Compile-Time Validation Tests
//--------------------------------------------------------------------------------------------------

#[test]
fn test_compile_time_constants() {
    // Test that the macro generates proper compile-time constants
    const _: () = {
        let err = MinimalError::Basic;
        // This should work at compile time if properly implemented
    };
}

//--------------------------------------------------------------------------------------------------
// Integration with Other Derive Macros
//--------------------------------------------------------------------------------------------------

#[derive(Debug, Clone, PartialEq, Eq, Hash, YoshiError)]
enum MultiDeriveError {
    #[yoshi(display = "Multi-derive test: {value}")]
    Test { value: i32 },
}

#[test]
fn test_multi_derive_compatibility() {
    let err1 = MultiDeriveError::Test { value: 42 };
    let err2 = err1.clone();

    assert_eq!(err1, err2);
    assert_eq!(format!("{}", err1), format!("{}", err2));

    use std::collections::HashSet;
    let mut set = HashSet::new();
    set.insert(err1);
    assert!(set.contains(&err2));
}

//--------------------------------------------------------------------------------------------------
// Documentation Generation Tests
//--------------------------------------------------------------------------------------------------

#[derive(Debug, YoshiError)]
/// This is a documented error enum
enum DocumentedError {
    /// This variant represents a documented error
    #[yoshi(display = "Documented error occurred")]
    Documented,

    /// Another documented variant with fields
    #[yoshi(display = "Complex documented error: {details}")]
    Complex {
        /// The error details
        details: String,
    },
}

#[test]
fn test_documentation_preservation() {
    // The macro should preserve documentation
    let err = DocumentedError::Documented;
    assert_eq!(format!("{}", err), "Documented error occurred");
}

//--------------------------------------------------------------------------------------------------
// Summary Test - Everything Together
//--------------------------------------------------------------------------------------------------

#[test]
fn test_comprehensive_edge_cases() {
    // Test a mix of edge cases to ensure they all work together
    let minimal = MinimalError::Basic;
    let complex = MaxComplexityError::UltraComplex {
        operation: "edge_test".to_string(),
        details: "testing".to_string(),
        error_code: None,
        custom_field: "test".to_string(),
        metadata: std::collections::HashMap::new(),
    };
    let unicode = UnicodeError::EmojiTest;
    let large = StressTestError::V75;

    // All should implement Error trait
    let errors: Vec<&dyn Error> = vec![&minimal, &complex, &unicode, &large];

    for err in errors {
        assert!(!format!("{}", err).is_empty());
        assert!(!format!("{:?}", err).is_empty());
    }

    // Test that helper methods work
    assert_eq!(minimal.variant_name(), "Basic");
    assert_eq!(complex.error_kind(), "UltraComplex");
    assert!(unicode.suggestion().is_some());
    assert_eq!(large.variant_name(), "V75");
}
