use std::error::Error;
use yoshi_derive::YoshiError;

#[derive(Debug, YoshiError)]
enum SimpleTestError {
    Basic,
    WithField { field: String },
}

#[derive(Debug, YoshiError)]
#[yoshi(namespace = "test")]
enum NamespaceTestError {
    Simple,
    #[yoshi(display = "Custom: {message}")]
    Custom { message: String },
}

#[test]
fn test_basic_functionality() {
    let err1 = SimpleTestError::Basic;
    let err2 = SimpleTestError::WithField {
        field: "test".to_string(),
    };

    println!("Basic: {}", err1);
    println!("WithField: {}", err2);

    assert_eq!(format!("{}", err1), "Basic");
    assert_eq!(format!("{}", err2), "WithField");
}

#[test]
fn test_namespace_and_explicit_display() {
    let err1 = NamespaceTestError::Simple;
    let err2 = NamespaceTestError::Custom {
        message: "hello".to_string(),
    };

    println!("Namespace Simple: {}", err1);
    println!("Namespace Custom: {}", err2);

    assert_eq!(format!("{}", err1), "test: Simple");
    assert_eq!(format!("{}", err2), "test: Custom: hello");
}
